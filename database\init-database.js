#!/usr/bin/env node

/**
 * Script d'initialisation de la base de données SQLite
 * Crée la base de données et insère les données d'exemple
 */

const Database = require('better-sqlite3');
const fs = require('fs');
const path = require('path');

// Configuration
const DB_PATH = path.join(__dirname, 'cmc_database.sqlite');
const SCHEMA_PATH = path.join(__dirname, 'schema.sql');
const SEED_PATH = path.join(__dirname, 'seed.sql');

console.log('🚀 Initialisation de la base de données CMC...');

try {
  // Supprimer l'ancienne base de données si elle existe
  if (fs.existsSync(DB_PATH)) {
    fs.unlinkSync(DB_PATH);
    console.log('✅ Ancienne base de données supprimée');
  }

  // Créer une nouvelle base de données
  const db = new Database(DB_PATH);
  console.log('✅ Nouvelle base de données créée');

  // Lire et exécuter le schéma
  const schema = fs.readFileSync(SCHEMA_PATH, 'utf8');
  
  // Diviser le schéma en instructions individuelles
  const statements = schema
    .split(';')
    .map(stmt => stmt.trim())
    .filter(stmt => stmt.length > 0);

  console.log('📋 Création des tables...');
  statements.forEach((statement, index) => {
    try {
      db.exec(statement);
      console.log(`   ✓ Instruction ${index + 1}/${statements.length} exécutée`);
    } catch (error) {
      console.error(`   ❌ Erreur instruction ${index + 1}:`, error.message);
    }
  });

  // Lire et exécuter les données d'exemple
  const seedData = fs.readFileSync(SEED_PATH, 'utf8');
  
  // Diviser les données en instructions individuelles
  const seedStatements = seedData
    .split(';')
    .map(stmt => stmt.trim())
    .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

  console.log('📊 Insertion des données d\'exemple...');
  seedStatements.forEach((statement, index) => {
    try {
      db.exec(statement);
      console.log(`   ✓ Données ${index + 1}/${seedStatements.length} insérées`);
    } catch (error) {
      console.error(`   ❌ Erreur données ${index + 1}:`, error.message);
    }
  });

  // Vérifier les données insérées
  console.log('\n📈 Vérification des données insérées:');
  
  const tables = [
    'utilisateurs',
    'actualites', 
    'formations',
    'evenements',
    'emplois_du_temps'
  ];

  tables.forEach(table => {
    try {
      const count = db.prepare(`SELECT COUNT(*) as count FROM ${table}`).get();
      console.log(`   📋 ${table}: ${count.count} enregistrements`);
    } catch (error) {
      console.error(`   ❌ Erreur vérification ${table}:`, error.message);
    }
  });

  // Fermer la connexion
  db.close();

  console.log('\n🎉 Base de données initialisée avec succès !');
  console.log(`📁 Fichier de base de données: ${DB_PATH}`);
  console.log('\n📝 Informations de connexion par défaut:');
  console.log('   Email: <EMAIL>');
  console.log('   Mot de passe: admin123');
  console.log('\n⚠️  N\'oubliez pas de changer les mots de passe par défaut !');

} catch (error) {
  console.error('❌ Erreur lors de l\'initialisation:', error.message);
  process.exit(1);
}
