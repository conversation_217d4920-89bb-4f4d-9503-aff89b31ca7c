// Configuration et utilitaires pour la base de données
// Utilisation du localStorage pour la simplicité (développement)

// Simulation d'une base de données avec localStorage

// Clés pour le localStorage
const STORAGE_KEYS = {
  USERS: 'cmc_users',
  ACTUALITES: 'cmc_actualites',
  FORMATIONS: 'cmc_formations',
  EVENEMENTS: 'cmc_evenements',
  EMPLOIS: 'cmc_emplois',
  SESSIONS: 'cmc_sessions'
};

// Fonction pour hasher les mots de passe (simulation simple)
function hashPassword(password) {
  // En production, utilisez bcrypt ou une vraie fonction de hash
  return btoa(password + 'salt_cmc_2024');
}

function verifyPassword(password, hash) {
  return hashPassword(password) === hash;
}

// Fonction pour obtenir des données du localStorage
export function getStorageData(key) {
  try {
    const data = localStorage.getItem(key);
    return data ? JSON.parse(data) : [];
  } catch (error) {
    console.error('Erreur lors de la lecture du localStorage:', error);
    return [];
  }
}

// Fonction pour sauvegarder des données dans le localStorage
export function setStorageData(key, data) {
  try {
    localStorage.setItem(key, JSON.stringify(data));
    return true;
  } catch (error) {
    console.error('Erreur lors de l\'écriture dans le localStorage:', error);
    return false;
  }
}

// Exporter les clés de stockage
export { STORAGE_KEYS };

// Initialisation de la base de données avec des données d'exemple
export function initDatabase() {
  try {
    // Vérifier si les données existent déjà
    const users = getStorageData(STORAGE_KEYS.USERS);

    if (users.length === 0) {
      // Insérer les données d'exemple
      const defaultUsers = [
        {
          id: 1,
          nom: "Martin",
          prenom: "Jean",
          email: "<EMAIL>",
          password_hash: hashPassword("admin123"),
          role: "Administrateur principal",
          status: "actif",
          date_creation: "2024-01-15",
          dernier_acces: "2024-03-20",
          mot_de_passe_temporaire: false
        },
        {
          id: 2,
          nom: "Dubois",
          prenom: "Marie",
          email: "<EMAIL>",
          password_hash: hashPassword("admin123"),
          role: "Administrateur",
          status: "actif",
          date_creation: "2024-02-10",
          dernier_acces: "2024-03-18",
          mot_de_passe_temporaire: false
        },
        {
          id: 3,
          nom: "Bernard",
          prenom: "Pierre",
          email: "<EMAIL>",
          password_hash: hashPassword("admin123"),
          role: "Administrateur lecture seule",
          status: "actif",
          date_creation: "2024-03-01",
          dernier_acces: "2024-03-15",
          mot_de_passe_temporaire: true
        }
      ];

      setStorageData(STORAGE_KEYS.USERS, defaultUsers);
      console.log('✅ Utilisateurs par défaut créés');
    }

    console.log('✅ Base de données initialisée');
    return true;
  } catch (error) {
    console.error('❌ Erreur lors de l\'initialisation:', error);
    return false;
  }
}

// Fonctions utilitaires pour les utilisateurs
export const UserService = {
  // Créer un nouvel utilisateur
  create: (userData) => {
    const users = getStorageData(STORAGE_KEYS.USERS);
    const newId = Math.max(...users.map(u => u.id), 0) + 1;

    const newUser = {
      id: newId,
      nom: userData.nom,
      prenom: userData.prenom,
      email: userData.email,
      password_hash: hashPassword(userData.password),
      role: userData.role || 'Administrateur',
      status: userData.status || 'actif',
      date_creation: new Date().toISOString().split('T')[0],
      dernier_acces: null,
      mot_de_passe_temporaire: true
    };

    users.push(newUser);
    setStorageData(STORAGE_KEYS.USERS, users);
    return { insertId: newId };
  },

  // Authentifier un utilisateur
  authenticate: (email, password) => {
    const users = getStorageData(STORAGE_KEYS.USERS);
    const user = users.find(u => u.email === email && u.status === 'actif');

    if (user && verifyPassword(password, user.password_hash)) {
      // Mettre à jour le dernier accès
      user.dernier_acces = new Date().toISOString();
      setStorageData(STORAGE_KEYS.USERS, users);

      // Retourner l'utilisateur sans le hash du mot de passe
      const { password_hash, ...userWithoutPassword } = user;
      return userWithoutPassword;
    }

    return null;
  },

  // Obtenir tous les utilisateurs
  getAll: () => {
    const users = getStorageData(STORAGE_KEYS.USERS);
    return users.map(({ password_hash, ...user }) => user);
  },

  // Obtenir un utilisateur par ID
  getById: (id) => {
    const users = getStorageData(STORAGE_KEYS.USERS);
    const user = users.find(u => u.id === parseInt(id));
    if (user) {
      const { password_hash, ...userWithoutPassword } = user;
      return userWithoutPassword;
    }
    return null;
  },

  // Mettre à jour un utilisateur
  update: (id, userData) => {
    const users = getStorageData(STORAGE_KEYS.USERS);
    const userIndex = users.findIndex(u => u.id === parseInt(id));

    if (userIndex !== -1) {
      users[userIndex] = {
        ...users[userIndex],
        nom: userData.nom,
        prenom: userData.prenom,
        email: userData.email,
        role: userData.role,
        status: userData.status,
        updated_at: new Date().toISOString()
      };

      setStorageData(STORAGE_KEYS.USERS, users);
      return { changes: 1 };
    }

    return { changes: 0 };
  },

  // Supprimer un utilisateur
  delete: (id) => {
    const users = getStorageData(STORAGE_KEYS.USERS);
    const filteredUsers = users.filter(u => u.id !== parseInt(id));

    if (filteredUsers.length < users.length) {
      setStorageData(STORAGE_KEYS.USERS, filteredUsers);
      return { changes: 1 };
    }

    return { changes: 0 };
  }
};

// Fonctions utilitaires pour les actualités
export const ActualiteService = {
  getAll: () => {
    const actualites = getStorageData(STORAGE_KEYS.ACTUALITES);
    const users = getStorageData(STORAGE_KEYS.USERS);

    return actualites.map(actualite => {
      const user = users.find(u => u.id === actualite.created_by);
      return {
        ...actualite,
        created_by_name: user ? `${user.prenom} ${user.nom}` : 'Inconnu'
      };
    }).sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
  },

  create: (data, userId) => {
    const actualites = getStorageData(STORAGE_KEYS.ACTUALITES);
    const newId = Math.max(...actualites.map(a => a.id), 0) + 1;

    const newActualite = {
      id: newId,
      title: data.title,
      content: data.content,
      category: data.category || 'formation',
      status: data.status || 'brouillon',
      image_url: data.image_url || '',
      author: data.author || '',
      date_publication: data.date_publication || new Date().toISOString().split('T')[0],
      created_by: userId,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    actualites.push(newActualite);
    setStorageData(STORAGE_KEYS.ACTUALITES, actualites);
    return { insertId: newId };
  },

  update: (id, data) => {
    const actualites = getStorageData(STORAGE_KEYS.ACTUALITES);
    const actualiteIndex = actualites.findIndex(a => a.id === parseInt(id));

    if (actualiteIndex !== -1) {
      actualites[actualiteIndex] = {
        ...actualites[actualiteIndex],
        title: data.title,
        content: data.content,
        category: data.category,
        status: data.status,
        image_url: data.image_url,
        author: data.author,
        updated_at: new Date().toISOString()
      };

      setStorageData(STORAGE_KEYS.ACTUALITES, actualites);
      return { changes: 1 };
    }

    return { changes: 0 };
  },

  delete: (id) => {
    const actualites = getStorageData(STORAGE_KEYS.ACTUALITES);
    const filteredActualites = actualites.filter(a => a.id !== parseInt(id));

    if (filteredActualites.length < actualites.length) {
      setStorageData(STORAGE_KEYS.ACTUALITES, filteredActualites);
      return { changes: 1 };
    }

    return { changes: 0 };
  }
};

// Importer et réexporter les autres services
export {
  FormationService,
  EvenementService,
  EmploiDuTempsService,
  SessionService
} from './database-services.js';
