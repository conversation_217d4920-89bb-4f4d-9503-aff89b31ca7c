#!/usr/bin/env node

// Script d'initialisation de la base de données
// Usage: node scripts/init-db.js

import { initDatabase, closeDatabase } from '../src/lib/database.js';
import fs from 'fs';
import path from 'path';

console.log('🚀 Initialisation de la base de données CMC...\n');

try {
  // Vérifier que les fichiers SQL existent
  const schemaPath = './database/schema.sql';
  const seedPath = './database/seed.sql';

  if (!fs.existsSync(schemaPath)) {
    console.error('❌ Fichier schema.sql introuvable:', schemaPath);
    process.exit(1);
  }

  if (!fs.existsSync(seedPath)) {
    console.error('❌ Fichier seed.sql introuvable:', seedPath);
    process.exit(1);
  }

  // Initialiser la base de données
  const db = initDatabase();

  // Vérifier que les tables ont été créées
  const tables = db.prepare(`
    SELECT name FROM sqlite_master 
    WHERE type='table' AND name NOT LIKE 'sqlite_%'
    ORDER BY name
  `).all();

  console.log('\n📊 Tables créées:');
  tables.forEach(table => {
    console.log(`  ✅ ${table.name}`);
  });

  // Vérifier les données d'exemple
  const userCount = db.prepare('SELECT COUNT(*) as count FROM utilisateurs').get();
  const actualiteCount = db.prepare('SELECT COUNT(*) as count FROM actualites').get();
  const formationCount = db.prepare('SELECT COUNT(*) as count FROM formations').get();
  const evenementCount = db.prepare('SELECT COUNT(*) as count FROM evenements').get();
  const emploiCount = db.prepare('SELECT COUNT(*) as count FROM emplois_du_temps').get();

  console.log('\n📈 Données insérées:');
  console.log(`  👥 Utilisateurs: ${userCount.count}`);
  console.log(`  📰 Actualités: ${actualiteCount.count}`);
  console.log(`  🎓 Formations: ${formationCount.count}`);
  console.log(`  🎉 Événements: ${evenementCount.count}`);
  console.log(`  📅 Emplois du temps: ${emploiCount.count}`);

  // Afficher les utilisateurs créés
  const users = db.prepare('SELECT nom, prenom, email, role FROM utilisateurs').all();
  console.log('\n👤 Comptes administrateurs créés:');
  users.forEach(user => {
    console.log(`  • ${user.prenom} ${user.nom} (${user.email}) - ${user.role}`);
  });

  console.log('\n🔐 Mot de passe par défaut: admin123');
  console.log('⚠️  Changez les mots de passe lors de la première connexion!\n');

  closeDatabase();
  console.log('✅ Initialisation terminée avec succès!');

} catch (error) {
  console.error('❌ Erreur lors de l\'initialisation:', error.message);
  process.exit(1);
}
