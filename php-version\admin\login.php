<?php
session_start();
require_once '../config/database.php';

$page_title = 'Connexion Administration';
$error_message = '';

// Vérifier si l'utilisateur est déjà connecté
if (isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true) {
    header('Location: dashboard.php');
    exit;
}

// Traitement du formulaire de connexion
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = trim($_POST['email'] ?? '');
    $password = trim($_POST['password'] ?? '');
    
    if (empty($email) || empty($password)) {
        $error_message = 'Veuillez remplir tous les champs';
    } else {
        $db = getDB();
        $user = $db->selectOne(
            "SELECT * FROM utilisateurs WHERE email = ? AND status = 'actif'",
            [$email]
        );
        
        // Pour la démo, on accepte n'importe quel mot de passe
        // En production, utilisez password_verify($password, $user['password_hash'])
        if ($user && ($password === 'admin123' || password_verify($password, $user['password_hash']))) {
            $_SESSION['admin_logged_in'] = true;
            $_SESSION['admin_user_id'] = $user['id'];
            $_SESSION['admin_user_name'] = $user['prenom'] . ' ' . $user['nom'];
            $_SESSION['admin_user_role'] = $user['role'];
            $_SESSION['admin_user_email'] = $user['email'];
            
            // Mettre à jour la dernière connexion
            $db->update(
                "UPDATE utilisateurs SET dernier_acces = NOW() WHERE id = ?",
                [$user['id']]
            );
            
            header('Location: dashboard.php');
            exit;
        } else {
            $error_message = 'Email ou mot de passe incorrect';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - CMC</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/admin.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
</head>
<body class="admin-body">
    <div class="login-container">
        <div class="login-card">
            <!-- Logo et titre -->
            <div class="login-header">
                <div class="login-logo">
                    <img src="../assets/images/logo.png" alt="CMC Logo" class="logo-img">
                    <h1 class="login-title">Administration CMC</h1>
                </div>
                <p class="login-subtitle">Connectez-vous pour accéder au panneau d'administration</p>
            </div>
            
            <!-- Formulaire de connexion -->
            <form method="POST" class="login-form" data-validate>
                <?php if ($error_message): ?>
                <div class="alert alert-error">
                    <i data-lucide="alert-circle"></i>
                    <span><?php echo htmlspecialchars($error_message); ?></span>
                </div>
                <?php endif; ?>
                
                <div class="form-group">
                    <label for="email" class="form-label">
                        <i data-lucide="mail"></i>
                        Adresse email
                    </label>
                    <input 
                        type="email" 
                        id="email" 
                        name="email" 
                        class="form-input"
                        placeholder="<EMAIL>"
                        value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>"
                        required
                    >
                </div>
                
                <div class="form-group">
                    <label for="password" class="form-label">
                        <i data-lucide="lock"></i>
                        Mot de passe
                    </label>
                    <div class="password-input">
                        <input 
                            type="password" 
                            id="password" 
                            name="password" 
                            class="form-input"
                            placeholder="Votre mot de passe"
                            required
                        >
                        <button type="button" class="password-toggle" onclick="togglePassword()">
                            <i data-lucide="eye" id="password-icon"></i>
                        </button>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="checkbox-label">
                        <input type="checkbox" name="remember" class="checkbox-input">
                        <span class="checkbox-custom"></span>
                        Se souvenir de moi
                    </label>
                </div>
                
                <button type="submit" class="btn btn-primary btn-full">
                    <i data-lucide="log-in"></i>
                    Se connecter
                </button>
            </form>
            
            <!-- Informations de test -->
            <div class="login-info">
                <h3>Comptes de test :</h3>
                <div class="test-accounts">
                    <div class="test-account">
                        <strong>Administrateur principal :</strong><br>
                        Email: <EMAIL><br>
                        Mot de passe: admin123
                    </div>
                    <div class="test-account">
                        <strong>Administrateur :</strong><br>
                        Email: <EMAIL><br>
                        Mot de passe: admin123
                    </div>
                    <div class="test-account">
                        <strong>Lecture seule :</strong><br>
                        Email: <EMAIL><br>
                        Mot de passe: admin123
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Lien retour -->
        <div class="login-footer">
            <a href="../index.php" class="back-link">
                <i data-lucide="arrow-left"></i>
                Retour au site
            </a>
        </div>
    </div>

    <script>
        // Toggle password visibility
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const passwordIcon = document.getElementById('password-icon');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                passwordIcon.setAttribute('data-lucide', 'eye-off');
            } else {
                passwordInput.type = 'password';
                passwordIcon.setAttribute('data-lucide', 'eye');
            }
            
            lucide.createIcons();
        }
        
        // Auto-fill pour la démo
        document.addEventListener('DOMContentLoaded', function() {
            lucide.createIcons();
            
            // Ajouter des boutons de remplissage automatique
            const testAccounts = document.querySelectorAll('.test-account');
            testAccounts.forEach((account, index) => {
                account.addEventListener('click', function() {
                    const emails = ['<EMAIL>', '<EMAIL>', '<EMAIL>'];
                    document.getElementById('email').value = emails[index];
                    document.getElementById('password').value = 'admin123';
                });
                account.style.cursor = 'pointer';
                account.title = 'Cliquer pour remplir automatiquement';
            });
        });
    </script>
    
    <script src="../assets/js/main.js"></script>
</body>
</html>
