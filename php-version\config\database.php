<?php
/**
 * Configuration de la base de données MySQL
 * Centre de Formation Agricole CMC
 */

// Configuration de la base de données
define('DB_HOST', 'localhost');
define('DB_NAME', 'cmc_database');
define('DB_USER', 'root'); // Changez selon votre configuration
define('DB_PASS', ''); // Changez selon votre configuration
define('DB_CHARSET', 'utf8mb4');

// Classe de connexion à la base de données
class Database {
    private static $instance = null;
    private $connection;
    
    private function __construct() {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
            $this->connection = new PDO($dsn, DB_USER, DB_PASS);
            $this->connection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->connection->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        } catch(PDOException $e) {
            die("Erreur de connexion à la base de données : " . $e->getMessage());
        }
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    public function getConnection() {
        return $this->connection;
    }
    
    // Méthode pour exécuter une requête SELECT
    public function select($query, $params = []) {
        try {
            $stmt = $this->connection->prepare($query);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch(PDOException $e) {
            error_log("Erreur SELECT : " . $e->getMessage());
            return false;
        }
    }
    
    // Méthode pour exécuter une requête SELECT (un seul résultat)
    public function selectOne($query, $params = []) {
        try {
            $stmt = $this->connection->prepare($query);
            $stmt->execute($params);
            return $stmt->fetch();
        } catch(PDOException $e) {
            error_log("Erreur SELECT ONE : " . $e->getMessage());
            return false;
        }
    }
    
    // Méthode pour exécuter une requête INSERT
    public function insert($query, $params = []) {
        try {
            $stmt = $this->connection->prepare($query);
            $stmt->execute($params);
            return $this->connection->lastInsertId();
        } catch(PDOException $e) {
            error_log("Erreur INSERT : " . $e->getMessage());
            return false;
        }
    }
    
    // Méthode pour exécuter une requête UPDATE
    public function update($query, $params = []) {
        try {
            $stmt = $this->connection->prepare($query);
            $stmt->execute($params);
            return $stmt->rowCount();
        } catch(PDOException $e) {
            error_log("Erreur UPDATE : " . $e->getMessage());
            return false;
        }
    }
    
    // Méthode pour exécuter une requête DELETE
    public function delete($query, $params = []) {
        try {
            $stmt = $this->connection->prepare($query);
            $stmt->execute($params);
            return $stmt->rowCount();
        } catch(PDOException $e) {
            error_log("Erreur DELETE : " . $e->getMessage());
            return false;
        }
    }
}

// Fonction helper pour obtenir la connexion
function getDB() {
    return Database::getInstance();
}
?>
