# Base de Données CMC

Ce dossier contient la configuration et les scripts de la base de données pour le site du Centre de Formation Agricole CMC.

## Structure

```
database/
├── README.md           # Ce fichier
├── schema.sql          # Schéma de la base de données (tables, index)
├── seed.sql           # Données d'exemple
└── cmc.db             # Fichier de base de données SQLite (généré)
```

## Installation

1. **Installer les dépendances** :
```bash
npm install
```

2. **Initialiser la base de données** :
```bash
npm run init-db
```

3. **Réinitialiser la base de données** (supprime et recrée) :
```bash
npm run reset-db
```

## Tables

### `utilisateurs`
Stocke les comptes administrateurs du système.
- **Champs** : id, nom, prenom, email, password_hash, role, status, dates
- **Rôles** : Administrateur principal, Administrateur, Administrateur lecture seule
- **Statuts** : actif, inactif, suspendu

### `actualites`
Stocke les actualités du site.
- **Champs** : id, title, content, category, status, image_url, author, dates
- **Catégories** : formation, infrastructure, partenariat, pedagogie, vie-etudiante
- **Statuts** : brouillon, publié

### `formations`
Stocke les formations proposées.
- **Champs** : id, title, description, level, duration, prerequisites, objectives, program, career, status
- **Niveaux** : CAP, Bac Pro, BTS, Licence, Formation continue
- **Statuts** : brouillon, publié

### `evenements`
Stocke les événements organisés.
- **Champs** : id, title, description, date_evenement, lieu, type, participants, contact, status
- **Types** : Salon, Conférence, Portes ouvertes, Sortie pédagogique, Concours, Cérémonie
- **Statuts** : brouillon, publié

### `emplois_du_temps`
Stocke les emplois du temps importés par filière et groupe.
- **Champs** : id, filiere, groupe, semaine, file_name, file_path, file_size, description, status, upload_date
- **Filières** : BTS ACSE, BTS PA, BTS PV, CAP Agriculture, CAP Elevage, Bac Pro CGEA, Bac Pro CGEH
- **Groupes** : 1ère année, 2ème année, Groupe A/B/C, Apprentissage, Formation Continue
- **Statuts** : brouillon, publié

### `sessions`
Stocke les sessions de connexion pour la sécurité.
- **Champs** : id, user_id, session_token, expires_at, created_at

## Comptes par défaut

Après l'initialisation, ces comptes administrateurs sont créés :

| Email | Nom | Rôle | Mot de passe |
|-------|-----|------|--------------|
| <EMAIL> | Jean Martin | Administrateur principal | admin123 |
| <EMAIL> | Marie Dubois | Administrateur | admin123 |
| <EMAIL> | Pierre Bernard | Administrateur lecture seule | admin123 |

⚠️ **Important** : Changez ces mots de passe lors de la première connexion !

## Utilisation

### Dans le code JavaScript

```javascript
import { 
  UserService, 
  ActualiteService, 
  FormationService, 
  EvenementService, 
  EmploiDuTempsService 
} from '../src/lib/database.js';

// Authentification
const user = UserService.authenticate('<EMAIL>', 'password');

// Récupérer toutes les actualités
const actualites = ActualiteService.getAll();

// Créer une nouvelle formation
const formation = FormationService.create({
  title: 'Nouvelle formation',
  description: 'Description...',
  level: 'BTS',
  // ...
}, userId);
```

### Requêtes SQL directes

```javascript
import { getDatabase } from '../src/lib/database.js';

const db = getDatabase();
const result = db.prepare('SELECT * FROM actualites WHERE status = ?').all('publié');
```

## Sécurité

- **Mots de passe** : Hashés avec bcrypt (salt rounds: 10)
- **Sessions** : Tokens UUID avec expiration (24h)
- **Validation** : Contraintes de clés étrangères activées
- **Nettoyage** : Sessions expirées supprimées automatiquement

## Sauvegarde

Pour sauvegarder la base de données :
```bash
cp database/cmc.db database/backup_$(date +%Y%m%d_%H%M%S).db
```

## Restauration

Pour restaurer une sauvegarde :
```bash
cp database/backup_YYYYMMDD_HHMMSS.db database/cmc.db
```

## Développement

- **SQLite** : Base de données légère, parfaite pour le développement
- **WAL Mode** : Améliore les performances en lecture/écriture
- **Index** : Optimisent les requêtes fréquentes
- **Transactions** : Garantissent la cohérence des données
