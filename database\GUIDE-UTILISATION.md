# 📚 Guide d'utilisation de la base de données CMC

## 🎯 Vue d'ensemble

Cette base de données JSON simple a été créée pour le Centre de Formation Agricole CMC. Elle contient toutes les données nécessaires pour gérer :
- Les utilisateurs administrateurs
- Les actualités du centre
- Les formations proposées
- Les événements organisés
- Les emplois du temps par filière et groupe

## 🚀 Initialisation

### 1. Créer la base de données

```bash
node database/init-simple-db.js
```

### 2. Tester la base de données

```bash
node database/test-db.js
```

## 📊 Structure des données

### 👥 Utilisateurs
```json
{
  "id": 1,
  "nom": "<PERSON>",
  "prenom": "Jean",
  "email": "<EMAIL>",
  "password_hash": "$2b$10$...",
  "role": "Administrateur principal",
  "status": "actif",
  "created_at": "2024-03-20T10:30:00Z",
  "updated_at": "2024-03-20T10:30:00Z"
}
```

### 📰 Actualités
```json
{
  "id": 1,
  "title": "Nouvelle formation en Agriculture Durable",
  "content": "Description complète...",
  "category": "formation",
  "status": "publié",
  "image_url": "/images/formation-agriculture.jpg",
  "author": "Direction Pédagogique",
  "date_publication": "2024-03-20",
  "created_by": 1
}
```

### 🎓 Formations
```json
{
  "id": 1,
  "title": "BTS ACSE",
  "description": "Formation complète...",
  "level": "BTS",
  "duration": "2 ans",
  "prerequisites": "Baccalauréat",
  "objectives": "Former des responsables...",
  "program": "Économie, Gestion...",
  "career": "Chef d'exploitation...",
  "status": "publié"
}
```

### 📅 Événements
```json
{
  "id": 1,
  "title": "Salon de l'Agriculture 2024",
  "description": "Participation du CMC...",
  "date": "2024-05-15",
  "lieu": "Paris Expo",
  "type": "Salon",
  "participants": "Étudiants BTS",
  "contact": "<EMAIL>",
  "status": "publié"
}
```

### 📋 Emplois du temps
```json
{
  "id": 1,
  "filiere": "BTS ACSE",
  "groupe": "1ère année",
  "semaine": "Semaine du 18-22 Mars 2024",
  "file_name": "emploi_bts_acse_1_mars.pdf",
  "file_path": "/uploads/emplois/emploi_bts_acse_1_mars.pdf",
  "file_size": 245760,
  "description": "Emploi du temps avec cours d'économie",
  "status": "publié"
}
```

## 💻 Utilisation dans le code

### Importer la base de données

```javascript
import SimpleDatabase from './database/simple-db.js';

// Créer une instance
const db = new SimpleDatabase();
```

### Opérations de base

```javascript
// Lire tous les enregistrements
const actualites = db.getAll('actualites');

// Lire un enregistrement par ID
const formation = db.getById('formations', 1);

// Rechercher avec critères
const actualitesPubliees = db.search('actualites', { status: 'publié' });
const formationsBTS = db.search('formations', { level: 'BTS' });

// Insérer un nouvel enregistrement
const newId = db.insert('actualites', {
  title: 'Nouvelle actualité',
  content: 'Contenu...',
  category: 'formation',
  status: 'brouillon',
  author: 'Admin',
  date_publication: '2024-06-15',
  created_by: 1
});

// Mettre à jour un enregistrement
const success = db.update('actualites', newId, {
  status: 'publié',
  title: 'Titre mis à jour'
});

// Supprimer un enregistrement
const deleted = db.delete('actualites', newId);
```

### Exemples spécifiques

```javascript
// Obtenir les emplois du temps d'une filière
const emploisBTSACSE = db.search('emplois_du_temps', { 
  filiere: 'BTS ACSE' 
});

// Obtenir les actualités d'une catégorie
const actualitesFormation = db.search('actualites', { 
  category: 'formation',
  status: 'publié'
});

// Obtenir les événements à venir
const evenements = db.getAll('evenements')
  .filter(event => new Date(event.date) > new Date())
  .sort((a, b) => new Date(a.date) - new Date(b.date));

// Obtenir les formations par niveau
const formationsCAP = db.search('formations', { level: 'CAP' });
const formationsBTS = db.search('formations', { level: 'BTS' });
```

## 🔐 Comptes par défaut

| Email | Mot de passe | Rôle |
|-------|-------------|------|
| <EMAIL> | admin123 | Administrateur principal |
| <EMAIL> | admin123 | Administrateur |
| <EMAIL> | admin123 | Lecture seule |

⚠️ **Important** : Changez ces mots de passe en production !

## 📁 Fichiers

- `simple-db.js` : Classe principale de la base de données
- `init-simple-db.js` : Script d'initialisation
- `test-db.js` : Script de test et démonstration
- `cmc_data.json` : Fichier de données (généré automatiquement)

## 🛠️ Maintenance

### Sauvegarder les données
```bash
cp database/cmc_data.json database/backup_$(date +%Y%m%d_%H%M%S).json
```

### Réinitialiser la base de données
```bash
node database/init-simple-db.js
```

### Vérifier l'intégrité
```bash
node database/test-db.js
```

## 🔄 Migration vers SQLite (optionnel)

Si vous souhaitez migrer vers SQLite plus tard :

1. Installez SQLite3 sur votre système
2. Utilisez les fichiers `schema.sql` et `seed.sql` fournis
3. Exécutez : `sqlite3 database/cmc.sqlite < database/schema.sql`
4. Puis : `sqlite3 database/cmc.sqlite < database/seed.sql`

## 📈 Avantages de cette solution

✅ **Aucune dépendance externe** - Fonctionne avec Node.js standard
✅ **Simple à utiliser** - API intuitive et claire
✅ **Portable** - Un seul fichier JSON
✅ **Rapide** - Chargement en mémoire
✅ **Flexible** - Facile à modifier et étendre
✅ **Compatible** - Fonctionne sur tous les systèmes

## 🎯 Prêt pour la production

Cette base de données est parfaite pour :
- Développement et tests
- Prototypage rapide
- Applications de petite à moyenne taille
- Démonstrations et formations

Pour des applications plus importantes, considérez une migration vers PostgreSQL ou MySQL.
