<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - ' : ''; ?>Administration CMC</title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/admin.css">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    
    <!-- Meta tags -->
    <meta name="robots" content="noindex, nofollow">
</head>
<body class="admin-body">
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="admin-sidebar" id="admin-sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <img src="../assets/images/logo.png" alt="CMC Logo" class="logo-img">
                    <div>
                        <h1 class="sidebar-title">CMC Admin</h1>
                        <p class="sidebar-subtitle">Centre de Formation</p>
                    </div>
                </div>
            </div>
            
            <nav class="sidebar-nav">
                <div class="nav-section">
                    <div class="nav-section-title">Principal</div>
                    <div class="nav-items">
                        <a href="dashboard.php" class="nav-item <?php echo basename($_SERVER['PHP_SELF']) == 'dashboard.php' ? 'active' : ''; ?>">
                            <i data-lucide="home"></i>
                            Tableau de bord
                        </a>
                    </div>
                </div>
                
                <div class="nav-section">
                    <div class="nav-section-title">Contenu</div>
                    <div class="nav-items">
                        <a href="actualites.php" class="nav-item <?php echo basename($_SERVER['PHP_SELF']) == 'actualites.php' ? 'active' : ''; ?>">
                            <i data-lucide="newspaper"></i>
                            Actualités
                        </a>
                        <a href="formations.php" class="nav-item <?php echo basename($_SERVER['PHP_SELF']) == 'formations.php' ? 'active' : ''; ?>">
                            <i data-lucide="graduation-cap"></i>
                            Formations
                        </a>
                        <a href="evenements.php" class="nav-item <?php echo basename($_SERVER['PHP_SELF']) == 'evenements.php' ? 'active' : ''; ?>">
                            <i data-lucide="calendar"></i>
                            Événements
                        </a>
                        <a href="emplois-du-temps.php" class="nav-item <?php echo basename($_SERVER['PHP_SELF']) == 'emplois-du-temps.php' ? 'active' : ''; ?>">
                            <i data-lucide="clock"></i>
                            Emplois du temps
                        </a>
                    </div>
                </div>
                
                <div class="nav-section">
                    <div class="nav-section-title">Administration</div>
                    <div class="nav-items">
                        <a href="utilisateurs.php" class="nav-item <?php echo basename($_SERVER['PHP_SELF']) == 'utilisateurs.php' ? 'active' : ''; ?>">
                            <i data-lucide="users"></i>
                            Utilisateurs
                        </a>
                        <a href="parametres.php" class="nav-item <?php echo basename($_SERVER['PHP_SELF']) == 'parametres.php' ? 'active' : ''; ?>">
                            <i data-lucide="settings"></i>
                            Paramètres
                        </a>
                    </div>
                </div>
                
                <div class="nav-section">
                    <div class="nav-section-title">Site</div>
                    <div class="nav-items">
                        <a href="../index.php" class="nav-item" target="_blank">
                            <i data-lucide="external-link"></i>
                            Voir le site
                        </a>
                    </div>
                </div>
            </nav>
        </aside>
        
        <!-- Contenu principal -->
        <main class="admin-main">
            <header class="admin-header">
                <div class="admin-header-left">
                    <button class="sidebar-toggle" id="sidebar-toggle">
                        <i data-lucide="menu"></i>
                    </button>
                    <h1 class="admin-title"><?php echo $page_title ?? 'Administration'; ?></h1>
                </div>
                
                <div class="admin-user">
                    <div class="user-info">
                        <div class="user-name"><?php echo htmlspecialchars($_SESSION['admin_user_name']); ?></div>
                        <div class="user-role"><?php echo htmlspecialchars($_SESSION['admin_user_role']); ?></div>
                    </div>
                    <div class="user-avatar">
                        <?php echo strtoupper(substr($_SESSION['admin_user_name'], 0, 2)); ?>
                    </div>
                    <a href="logout.php" class="logout-btn" title="Se déconnecter">
                        <i data-lucide="log-out"></i>
                        Déconnexion
                    </a>
                </div>
            </header>
            
            <div class="admin-content">
                <!-- Le contenu de la page sera inséré ici -->
