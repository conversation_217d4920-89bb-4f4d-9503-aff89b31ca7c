/* 
 * CSS Administration - Centre de Formation Agricole CMC
 * Reproduction exacte de l'interface d'administration React
 */

/* ========================================
   LAYOUT ADMINISTRATION
======================================== */

.admin-body {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  min-height: 100vh;
  font-family: var(--font-family);
}

/* ========================================
   PAGE DE CONNEXION
======================================== */

.login-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-4);
}

.login-card {
  background: white;
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-xl);
  padding: var(--spacing-8);
  width: 100%;
  max-width: 450px;
  border: 1px solid var(--color-gray-200);
}

.login-header {
  text-align: center;
  margin-bottom: var(--spacing-8);
}

.login-logo {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-6);
}

.login-logo .logo-img {
  width: 4rem;
  height: 4rem;
}

.login-title {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--color-gray-900);
  margin: 0;
}

.login-subtitle {
  color: var(--color-gray-600);
  font-size: var(--font-size-base);
  margin: 0;
}

/* Formulaire de connexion */
.login-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-6);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.form-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-weight: 500;
  color: var(--color-gray-700);
  font-size: var(--font-size-sm);
}

.form-input {
  padding: var(--spacing-3) var(--spacing-4);
  border: 1px solid var(--color-gray-300);
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  transition: var(--transition);
  background-color: white;
}

.form-input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(22, 163, 74, 0.1);
}

.form-input.error {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.password-input {
  position: relative;
}

.password-toggle {
  position: absolute;
  right: var(--spacing-3);
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--color-gray-400);
  cursor: pointer;
  padding: var(--spacing-1);
  border-radius: var(--radius);
  transition: var(--transition);
}

.password-toggle:hover {
  color: var(--color-gray-600);
  background-color: var(--color-gray-100);
}

/* Checkbox personnalisé */
.checkbox-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  cursor: pointer;
  font-size: var(--font-size-sm);
  color: var(--color-gray-700);
}

.checkbox-input {
  display: none;
}

.checkbox-custom {
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid var(--color-gray-300);
  border-radius: var(--radius);
  position: relative;
  transition: var(--transition);
}

.checkbox-input:checked + .checkbox-custom {
  background-color: var(--color-primary);
  border-color: var(--color-primary);
}

.checkbox-input:checked + .checkbox-custom::after {
  content: '';
  position: absolute;
  top: 2px;
  left: 6px;
  width: 6px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

/* Boutons */
.btn-full {
  width: 100%;
  justify-content: center;
  padding: var(--spacing-4);
  font-size: var(--font-size-base);
  font-weight: 600;
}

/* Alertes */
.alert {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-4);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  margin-bottom: var(--spacing-4);
}

.alert-error {
  background-color: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.alert-success {
  background-color: #f0fdf4;
  color: #16a34a;
  border: 1px solid #bbf7d0;
}

.alert-warning {
  background-color: #fffbeb;
  color: #d97706;
  border: 1px solid #fed7aa;
}

.alert-info {
  background-color: #eff6ff;
  color: #2563eb;
  border: 1px solid #bfdbfe;
}

/* Informations de test */
.login-info {
  margin-top: var(--spacing-8);
  padding: var(--spacing-6);
  background-color: var(--color-gray-50);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-gray-200);
}

.login-info h3 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--color-gray-900);
  margin-bottom: var(--spacing-4);
}

.test-accounts {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.test-account {
  padding: var(--spacing-4);
  background-color: white;
  border-radius: var(--radius-md);
  border: 1px solid var(--color-gray-200);
  font-size: var(--font-size-sm);
  line-height: 1.5;
  transition: var(--transition);
}

.test-account:hover {
  border-color: var(--color-primary);
  box-shadow: var(--shadow-sm);
}

.test-account strong {
  color: var(--color-primary);
}

/* Footer de connexion */
.login-footer {
  margin-top: var(--spacing-8);
}

.back-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  color: var(--color-gray-600);
  text-decoration: none;
  font-size: var(--font-size-sm);
  transition: var(--transition);
}

.back-link:hover {
  color: var(--color-primary);
}

/* Erreurs de champ */
.field-error {
  color: #dc2626;
  font-size: var(--font-size-xs);
  margin-top: var(--spacing-1);
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
}

.field-error::before {
  content: '⚠';
  font-size: var(--font-size-xs);
}

/* ========================================
   DASHBOARD ADMINISTRATION
======================================== */

.admin-layout {
  display: flex;
  min-height: 100vh;
}

/* Sidebar */
.admin-sidebar {
  width: 280px;
  background-color: white;
  border-right: 1px solid var(--color-gray-200);
  box-shadow: var(--shadow-sm);
  position: fixed;
  height: 100vh;
  overflow-y: auto;
  z-index: 40;
}

.sidebar-header {
  padding: var(--spacing-6);
  border-bottom: 1px solid var(--color-gray-200);
}

.sidebar-logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.sidebar-logo .logo-img {
  width: 2.5rem;
  height: 2.5rem;
}

.sidebar-title {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--color-gray-900);
  margin: 0;
}

.sidebar-subtitle {
  font-size: var(--font-size-sm);
  color: var(--color-gray-500);
  margin: 0;
}

/* Navigation sidebar */
.sidebar-nav {
  padding: var(--spacing-4);
}

.nav-section {
  margin-bottom: var(--spacing-6);
}

.nav-section-title {
  font-size: var(--font-size-xs);
  font-weight: 600;
  color: var(--color-gray-500);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: var(--spacing-3);
  padding: 0 var(--spacing-3);
}

.nav-items {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
}

.nav-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-3);
  color: var(--color-gray-700);
  text-decoration: none;
  border-radius: var(--radius-md);
  font-weight: 500;
  transition: var(--transition);
}

.nav-item:hover {
  background-color: var(--color-gray-100);
  color: var(--color-gray-900);
}

.nav-item.active {
  background-color: var(--color-primary);
  color: white;
}

.nav-item.active:hover {
  background-color: var(--color-primary-dark);
}

/* Contenu principal */
.admin-main {
  flex: 1;
  margin-left: 280px;
  background-color: var(--color-gray-50);
}

.admin-header {
  background-color: white;
  border-bottom: 1px solid var(--color-gray-200);
  padding: var(--spacing-4) var(--spacing-6);
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: sticky;
  top: 0;
  z-index: 30;
}

.admin-title {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--color-gray-900);
  margin: 0;
}

.admin-user {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
}

.user-info {
  text-align: right;
}

.user-name {
  font-weight: 600;
  color: var(--color-gray-900);
  font-size: var(--font-size-sm);
}

.user-role {
  font-size: var(--font-size-xs);
  color: var(--color-gray-500);
}

.user-avatar {
  width: 2.5rem;
  height: 2.5rem;
  background-color: var(--color-primary);
  color: white;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
}

.logout-btn {
  padding: var(--spacing-2) var(--spacing-3);
  background-color: var(--color-gray-100);
  color: var(--color-gray-700);
  border: none;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-size: var(--font-size-sm);
}

.logout-btn:hover {
  background-color: var(--color-gray-200);
}

.admin-content {
  padding: var(--spacing-6);
}

.admin-header-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
}

.sidebar-toggle {
  display: none;
  background: none;
  border: none;
  color: var(--color-gray-600);
  cursor: pointer;
  padding: var(--spacing-2);
  border-radius: var(--radius);
  transition: var(--transition);
}

.sidebar-toggle:hover {
  background-color: var(--color-gray-100);
  color: var(--color-gray-900);
}

/* ========================================
   DASHBOARD STYLES
======================================== */

.dashboard-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-8);
}

/* Stats Overview */
.stats-overview {
  background-color: white;
  border-radius: var(--radius-lg);
  padding: var(--spacing-6);
  box-shadow: var(--shadow);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-6);
  margin-top: var(--spacing-6);
}

.stat-card {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
  padding: var(--spacing-6);
  background-color: var(--color-gray-50);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-gray-200);
  transition: var(--transition);
}

.stat-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.stat-icon {
  width: 3rem;
  height: 3rem;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.stat-icon-blue { background-color: #3b82f6; }
.stat-icon-green { background-color: #10b981; }
.stat-icon-yellow { background-color: #f59e0b; }
.stat-icon-purple { background-color: #8b5cf6; }
.stat-icon-red { background-color: #ef4444; }

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--color-gray-900);
  line-height: 1;
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--color-gray-600);
  margin-top: var(--spacing-1);
}

/* Quick Actions */
.quick-actions {
  background-color: white;
  border-radius: var(--radius-lg);
  padding: var(--spacing-6);
  box-shadow: var(--shadow);
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-4);
  margin-top: var(--spacing-6);
}

.action-card {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
  padding: var(--spacing-4);
  border: 2px solid var(--color-gray-200);
  border-radius: var(--radius-lg);
  text-decoration: none;
  color: var(--color-gray-700);
  transition: var(--transition);
}

.action-card:hover {
  border-color: var(--color-primary);
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.action-icon {
  width: 2.5rem;
  height: 2.5rem;
  background-color: var(--color-primary);
  color: white;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-content h3 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--color-gray-900);
  margin: 0 0 var(--spacing-1) 0;
}

.action-content p {
  font-size: var(--font-size-sm);
  color: var(--color-gray-600);
  margin: 0;
}

/* Dashboard Grid */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--spacing-6);
}

.dashboard-section {
  background-color: white;
  border-radius: var(--radius-lg);
  padding: var(--spacing-6);
  box-shadow: var(--shadow);
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-6);
  padding-bottom: var(--spacing-4);
  border-bottom: 1px solid var(--color-gray-200);
}

.section-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--color-gray-900);
  margin: 0;
}

/* Content List */
.content-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.content-item {
  padding: var(--spacing-4);
  border: 1px solid var(--color-gray-200);
  border-radius: var(--radius-md);
  transition: var(--transition);
}

.content-item:hover {
  border-color: var(--color-primary);
  box-shadow: var(--shadow-sm);
}

.content-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-3);
}

.content-date {
  font-size: var(--font-size-xs);
  color: var(--color-gray-500);
}

.content-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  margin: 0 0 var(--spacing-2) 0;
}

.content-title a {
  color: var(--color-gray-900);
  text-decoration: none;
}

.content-title a:hover {
  color: var(--color-primary);
}

.content-excerpt {
  font-size: var(--font-size-sm);
  color: var(--color-gray-600);
  line-height: 1.5;
  margin: 0 0 var(--spacing-3) 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.content-actions {
  display: flex;
  gap: var(--spacing-2);
}

.btn-xs {
  padding: var(--spacing-1) var(--spacing-2);
  font-size: var(--font-size-xs);
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: var(--spacing-8);
  color: var(--color-gray-500);
}

.empty-state i {
  font-size: 3rem;
  margin-bottom: var(--spacing-4);
  opacity: 0.5;
}

.empty-state p {
  margin: 0 0 var(--spacing-4) 0;
  font-size: var(--font-size-lg);
}

/* Activity Section */
.activity-section {
  background-color: white;
  border-radius: var(--radius-lg);
  padding: var(--spacing-6);
  box-shadow: var(--shadow);
}

.activity-feed {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
  margin-top: var(--spacing-6);
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-4);
  padding: var(--spacing-4);
  border-radius: var(--radius-md);
  background-color: var(--color-gray-50);
}

.activity-icon {
  width: 2rem;
  height: 2rem;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.activity-icon-create { background-color: #10b981; }
.activity-icon-edit { background-color: #3b82f6; }
.activity-icon-delete { background-color: #ef4444; }
.activity-icon-info { background-color: #6b7280; }

.activity-content {
  flex: 1;
}

.activity-content p {
  margin: 0 0 var(--spacing-1) 0;
  font-size: var(--font-size-sm);
  color: var(--color-gray-900);
}

.activity-time {
  font-size: var(--font-size-xs);
  color: var(--color-gray-500);
}

/* ========================================
   RESPONSIVE ADMIN
======================================== */

@media (max-width: 768px) {
  .admin-sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }
  
  .admin-sidebar.open {
    transform: translateX(0);
  }
  
  .admin-main {
    margin-left: 0;
  }
  
  .login-card {
    padding: var(--spacing-6);
    margin: var(--spacing-4);
  }
  
  .test-accounts {
    gap: var(--spacing-3);
  }
  
  .test-account {
    padding: var(--spacing-3);
    font-size: var(--font-size-xs);
  }
}
