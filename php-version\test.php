<?php
// Test simple pour vérifier que PHP fonctionne
echo "<h1>Test PHP - CMC</h1>";
echo "<p>PHP fonctionne ! Version : " . phpversion() . "</p>";
echo "<p>Date/Heure : " . date('Y-m-d H:i:s') . "</p>";

// Test de la base de données
try {
    require_once 'config/database.php';
    echo "<p style='color: green;'>✅ Fichier de configuration chargé</p>";
    
    $db = getDB();
    echo "<p style='color: green;'>✅ Connexion à la base de données réussie</p>";
    
    // Test d'une requête simple
    $result = $db->select("SELECT COUNT(*) as count FROM utilisateurs");
    if ($result) {
        echo "<p style='color: green;'>✅ Requête test réussie - " . $result[0]['count'] . " utilisateurs trouvés</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Erreur : " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h2>Informations du serveur :</h2>";
echo "<ul>";
echo "<li>Serveur : " . $_SERVER['SERVER_SOFTWARE'] . "</li>";
echo "<li>Document Root : " . $_SERVER['DOCUMENT_ROOT'] . "</li>";
echo "<li>Script : " . $_SERVER['SCRIPT_NAME'] . "</li>";
echo "<li>URL : " . $_SERVER['REQUEST_URI'] . "</li>";
echo "</ul>";

echo "<hr>";
echo "<h2>Extensions PHP :</h2>";
echo "<ul>";
echo "<li>PDO : " . (extension_loaded('pdo') ? '✅ Installé' : '❌ Non installé') . "</li>";
echo "<li>PDO MySQL : " . (extension_loaded('pdo_mysql') ? '✅ Installé' : '❌ Non installé') . "</li>";
echo "<li>MySQLi : " . (extension_loaded('mysqli') ? '✅ Installé' : '❌ Non installé') . "</li>";
echo "</ul>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1 { color: #16a34a; }
ul { background: #f5f5f5; padding: 15px; border-radius: 5px; }
</style>
