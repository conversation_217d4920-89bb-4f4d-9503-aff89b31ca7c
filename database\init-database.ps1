# Script PowerShell pour initialiser la base de données SQLite
# Utilisation: .\database\init-database.ps1

Write-Host "🚀 Initialisation de la base de données CMC..." -ForegroundColor Green

# Vérifier si SQLite3 est installé
try {
    $sqliteVersion = sqlite3 -version
    Write-Host "✅ SQLite3 détecté: $sqliteVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ SQLite3 n'est pas installé ou pas dans le PATH" -ForegroundColor Red
    Write-Host "📥 Téléchargez SQLite3 depuis: https://www.sqlite.org/download.html" -ForegroundColor Yellow
    exit 1
}

# Chemins des fichiers
$dbPath = "database\cmc_database.sqlite"
$schemaPath = "database\schema.sql"
$seedPath = "database\seed.sql"

# Supprimer l'ancienne base de données si elle existe
if (Test-Path $dbPath) {
    Remove-Item $dbPath
    Write-Host "✅ Ancienne base de données supprimée" -ForegroundColor Green
}

# Créer la base de données et exécuter le schéma
Write-Host "📋 Création des tables..." -ForegroundColor Blue
try {
    $schemaContent = Get-Content $schemaPath -Raw
    $schemaContent | sqlite3 $dbPath
    Write-Host "✅ Tables créées avec succès" -ForegroundColor Green
} catch {
    Write-Host "❌ Erreur lors de la création des tables: $_" -ForegroundColor Red
    exit 1
}

# Insérer les données d'exemple
Write-Host "📊 Insertion des données d'exemple..." -ForegroundColor Blue
try {
    $seedContent = Get-Content $seedPath -Raw
    $seedContent | sqlite3 $dbPath
    Write-Host "✅ Données d'exemple insérées" -ForegroundColor Green
} catch {
    Write-Host "❌ Erreur lors de l'insertion des données: $_" -ForegroundColor Red
    exit 1
}

# Vérifier les données insérées
Write-Host "`n📈 Vérification des données insérées:" -ForegroundColor Blue

$tables = @("utilisateurs", "actualites", "formations", "evenements", "emplois_du_temps")

foreach ($table in $tables) {
    try {
        $count = sqlite3 $dbPath "SELECT COUNT(*) FROM $table;"
        Write-Host "   📋 $table : $count enregistrements" -ForegroundColor Cyan
    } catch {
        Write-Host "   ❌ Erreur vérification $table : $_" -ForegroundColor Red
    }
}

Write-Host "`n🎉 Base de données initialisée avec succès !" -ForegroundColor Green
Write-Host "📁 Fichier de base de données: $dbPath" -ForegroundColor Yellow
Write-Host "`n📝 Informations de connexion par défaut:" -ForegroundColor Yellow
Write-Host "   Email: <EMAIL>" -ForegroundColor White
Write-Host "   Mot de passe: admin123" -ForegroundColor White
Write-Host "`n⚠️  N'oubliez pas de changer les mots de passe par défaut !" -ForegroundColor Red
