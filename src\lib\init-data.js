// Initialisation des données d'exemple pour la base de données
import { getStorageData, setStorageData, STORAGE_KEYS } from './database.js';

// Fonction pour hasher les mots de passe (simulation simple)
function hashPassword(password) {
  return btoa(password + 'salt_cmc_2024');
}

// Initialiser toutes les données d'exemple
export function initAllData() {
  initUsers();
  initActualites();
  initFormations();
  initEvenements();
  initEmploisDuTemps();
  console.log('✅ Toutes les données d\'exemple ont été initialisées');
}

// Initialiser les utilisateurs
function initUsers() {
  const users = getStorageData(STORAGE_KEYS.USERS);
  
  if (users.length === 0) {
    const defaultUsers = [
      {
        id: 1,
        nom: "<PERSON>",
        prenom: "<PERSON>",
        email: "<EMAIL>",
        password_hash: hashPassword("admin123"),
        role: "Administrateur principal",
        status: "actif",
        date_creation: "2024-01-15",
        dernier_acces: "2024-03-20",
        mot_de_passe_temporaire: false
      },
      {
        id: 2,
        nom: "Dubois",
        prenom: "Marie",
        email: "<EMAIL>",
        password_hash: hashPassword("admin123"),
        role: "Administrateur",
        status: "actif",
        date_creation: "2024-02-10",
        dernier_acces: "2024-03-18",
        mot_de_passe_temporaire: false
      },
      {
        id: 3,
        nom: "Bernard",
        prenom: "Pierre",
        email: "<EMAIL>",
        password_hash: hashPassword("admin123"),
        role: "Administrateur lecture seule",
        status: "actif",
        date_creation: "2024-03-01",
        dernier_acces: "2024-03-15",
        mot_de_passe_temporaire: true
      }
    ];

    setStorageData(STORAGE_KEYS.USERS, defaultUsers);
    console.log('✅ Utilisateurs par défaut créés');
  }
}

// Initialiser les actualités
function initActualites() {
  const actualites = getStorageData(STORAGE_KEYS.ACTUALITES);
  
  if (actualites.length === 0) {
    const defaultActualites = [
      {
        id: 1,
        title: "Nouvelle formation en Agriculture Durable",
        content: "Nous sommes ravis d'annoncer le lancement de notre nouvelle formation en Agriculture Durable. Cette formation innovante permettra aux étudiants d'acquérir les compétences nécessaires pour développer une agriculture respectueuse de l'environnement.",
        category: "formation",
        status: "publié",
        image_url: "/images/formation-agriculture.jpg",
        author: "Direction Pédagogique",
        date_publication: "2024-03-20",
        created_by: 1,
        created_at: "2024-03-20T10:00:00Z",
        updated_at: "2024-03-20T10:00:00Z"
      },
      {
        id: 2,
        title: "Journée Portes Ouvertes 2024",
        content: "Venez découvrir nos formations et nos installations lors de notre journée portes ouvertes le 15 avril 2024. Au programme : visites guidées, rencontres avec les enseignants et les étudiants, démonstrations pratiques.",
        category: "vie-etudiante",
        status: "publié",
        image_url: "/images/portes-ouvertes.jpg",
        author: "Service Communication",
        date_publication: "2024-04-15",
        created_by: 2,
        created_at: "2024-03-15T14:30:00Z",
        updated_at: "2024-03-15T14:30:00Z"
      },
      {
        id: 3,
        title: "Nouveau partenariat avec la Chambre d'Agriculture",
        content: "Le CMC vient de signer un partenariat stratégique avec la Chambre d'Agriculture locale pour renforcer l'insertion professionnelle de nos diplômés.",
        category: "partenariat",
        status: "brouillon",
        image_url: "/images/partenariat.jpg",
        author: "Direction",
        date_publication: "2024-03-25",
        created_by: 1,
        created_at: "2024-03-22T09:15:00Z",
        updated_at: "2024-03-22T09:15:00Z"
      }
    ];

    setStorageData(STORAGE_KEYS.ACTUALITES, defaultActualites);
    console.log('✅ Actualités par défaut créées');
  }
}

// Initialiser les formations
function initFormations() {
  const formations = getStorageData(STORAGE_KEYS.FORMATIONS);
  
  if (formations.length === 0) {
    const defaultFormations = [
      {
        id: 1,
        title: "BTS Analyse, Conduite et Stratégie de l'Entreprise Agricole (ACSE)",
        description: "Formation complète en gestion d'entreprise agricole permettant d'acquérir les compétences nécessaires pour diriger une exploitation agricole moderne.",
        level: "BTS",
        duration: "2 ans",
        prerequisites: "Baccalauréat (toutes séries)",
        objectives: "Former des responsables d'exploitation agricole capables de gérer une entreprise dans sa globalité",
        program: "Économie générale et agricole, Gestion d'entreprise, Comptabilité, Techniques agricoles, Zootechnie, Agronomie, Communication",
        career: "Chef d'exploitation agricole, Conseiller agricole, Technicien en agriculture, Responsable d'élevage",
        status: "publié",
        created_by: 1,
        created_at: "2024-01-20T10:00:00Z",
        updated_at: "2024-01-20T10:00:00Z"
      },
      {
        id: 2,
        title: "CAP Métiers de l'Agriculture",
        description: "Formation pratique aux métiers de l'agriculture avec une approche concrète du terrain.",
        level: "CAP",
        duration: "2 ans",
        prerequisites: "3ème ou équivalent",
        objectives: "Acquérir les compétences de base en agriculture et élevage",
        program: "Productions végétales, Productions animales, Agroéquipement, Techniques agricoles, Sécurité au travail",
        career: "Ouvrier agricole, Aide-éleveur, Conducteur d'engins agricoles",
        status: "publié",
        created_by: 1,
        created_at: "2024-01-25T14:00:00Z",
        updated_at: "2024-01-25T14:00:00Z"
      }
    ];

    setStorageData(STORAGE_KEYS.FORMATIONS, defaultFormations);
    console.log('✅ Formations par défaut créées');
  }
}

// Initialiser les événements
function initEvenements() {
  const evenements = getStorageData(STORAGE_KEYS.EVENEMENTS);
  
  if (evenements.length === 0) {
    const defaultEvenements = [
      {
        id: 1,
        title: "Salon de l'Agriculture 2024",
        description: "Participation du CMC au Salon International de l'Agriculture à Paris. Nos étudiants présenteront leurs projets et rencontreront les professionnels du secteur.",
        date_evenement: "2024-05-15",
        lieu: "Paris Expo Porte de Versailles",
        type: "Salon",
        participants: "Étudiants BTS et Bac Pro",
        contact: "<EMAIL>",
        status: "publié",
        created_by: 1,
        created_at: "2024-02-10T11:00:00Z",
        updated_at: "2024-02-10T11:00:00Z"
      },
      {
        id: 2,
        title: "Conférence sur l'Agriculture Biologique",
        description: "Conférence animée par des experts en agriculture biologique sur les enjeux et perspectives du secteur.",
        date_evenement: "2024-04-20",
        lieu: "Amphithéâtre du CMC",
        type: "Conférence",
        participants: "Étudiants, enseignants, professionnels",
        contact: "<EMAIL>",
        status: "publié",
        created_by: 2,
        created_at: "2024-03-01T16:00:00Z",
        updated_at: "2024-03-01T16:00:00Z"
      }
    ];

    setStorageData(STORAGE_KEYS.EVENEMENTS, defaultEvenements);
    console.log('✅ Événements par défaut créés');
  }
}

// Initialiser les emplois du temps
function initEmploisDuTemps() {
  const emplois = getStorageData(STORAGE_KEYS.EMPLOIS);
  
  if (emplois.length === 0) {
    const defaultEmplois = [
      {
        id: 1,
        filiere: "BTS ACSE",
        groupe: "1ère année",
        semaine: "Semaine du 18-22 Mars 2024",
        file_name: "emploi_bts_acse_1_mars.pdf",
        file_path: "/uploads/emplois/emploi_bts_acse_1_mars.pdf",
        file_size: 245760,
        description: "Emploi du temps avec cours d'économie et travaux pratiques",
        status: "publié",
        upload_date: "2024-03-15",
        created_by: 1,
        created_at: "2024-03-15T08:00:00Z",
        updated_at: "2024-03-15T08:00:00Z"
      },
      {
        id: 2,
        filiere: "BTS ACSE",
        groupe: "2ème année",
        semaine: "Semaine du 18-22 Mars 2024",
        file_name: "emploi_bts_acse_2_mars.pdf",
        file_path: "/uploads/emplois/emploi_bts_acse_2_mars.pdf",
        file_size: 198432,
        description: "Emploi du temps avec stage en entreprise",
        status: "publié",
        upload_date: "2024-03-15",
        created_by: 2,
        created_at: "2024-03-15T08:30:00Z",
        updated_at: "2024-03-15T08:30:00Z"
      },
      {
        id: 3,
        filiere: "CAP Agriculture",
        groupe: "Groupe A",
        semaine: "Semaine du 25-29 Mars 2024",
        file_name: "emploi_cap_agri_a_mars.pdf",
        file_path: "/uploads/emplois/emploi_cap_agri_a_mars.pdf",
        file_size: 189234,
        description: "Emploi du temps avec travaux pratiques en serre",
        status: "publié",
        upload_date: "2024-03-20",
        created_by: 1,
        created_at: "2024-03-20T09:15:00Z",
        updated_at: "2024-03-20T09:15:00Z"
      },
      {
        id: 4,
        filiere: "Bac Pro CGEA",
        groupe: "Apprentissage",
        semaine: "Semaine du 1-5 Avril 2024",
        file_name: "emploi_bac_pro_apprentis_avril.pdf",
        file_path: "/uploads/emplois/emploi_bac_pro_apprentis_avril.pdf",
        file_size: 167890,
        description: "Planning alternance entreprise/centre de formation",
        status: "brouillon",
        upload_date: "2024-03-25",
        created_by: 1,
        created_at: "2024-03-25T14:20:00Z",
        updated_at: "2024-03-25T14:20:00Z"
      }
    ];

    setStorageData(STORAGE_KEYS.EMPLOIS, defaultEmplois);
    console.log('✅ Emplois du temps par défaut créés');
  }
}

// Fonction pour réinitialiser toutes les données
export function resetAllData() {
  localStorage.removeItem(STORAGE_KEYS.USERS);
  localStorage.removeItem(STORAGE_KEYS.ACTUALITES);
  localStorage.removeItem(STORAGE_KEYS.FORMATIONS);
  localStorage.removeItem(STORAGE_KEYS.EVENEMENTS);
  localStorage.removeItem(STORAGE_KEYS.EMPLOIS);
  localStorage.removeItem(STORAGE_KEYS.SESSIONS);
  
  initAllData();
  console.log('✅ Toutes les données ont été réinitialisées');
}

// Fonction pour obtenir des statistiques
export function getDataStats() {
  const users = getStorageData(STORAGE_KEYS.USERS);
  const actualites = getStorageData(STORAGE_KEYS.ACTUALITES);
  const formations = getStorageData(STORAGE_KEYS.FORMATIONS);
  const evenements = getStorageData(STORAGE_KEYS.EVENEMENTS);
  const emplois = getStorageData(STORAGE_KEYS.EMPLOIS);
  
  return {
    users: users.length,
    actualites: actualites.length,
    formations: formations.length,
    evenements: evenements.length,
    emplois: emplois.length
  };
}
