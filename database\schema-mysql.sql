-- Base de données MySQL pour le Centre de Formation Agricole CMC
-- Version adaptée pour phpMyAdmin / MySQL

-- Utiliser la base de données
USE cmc_database;

-- Table des utilisateurs administrateurs
CREATE TABLE IF NOT EXISTS utilisateurs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(100) NOT NULL,
    prenom VARCHAR(100) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role ENUM('Administrateur principal', 'Administrateur', 'Administrateur lecture seule') DEFAULT 'Administrateur',
    status ENUM('actif', 'inactif', 'suspendu') DEFAULT 'actif',
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    dernier_acces TIMESTAMP NULL,
    mot_de_passe_temporaire BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table des actualités
CREATE TABLE IF NOT EXISTS actualites (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    category ENUM('formation', 'infrastructure', 'partenariat', 'pedagogie', 'vie-etudiante') DEFAULT 'formation',
    status ENUM('brouillon', 'publié') DEFAULT 'brouillon',
    image_url VARCHAR(500),
    author VARCHAR(100),
    date_publication DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    FOREIGN KEY (created_by) REFERENCES utilisateurs(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table des formations
CREATE TABLE IF NOT EXISTS formations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    level ENUM('CAP', 'Bac Pro', 'BTS', 'Licence', 'Formation continue') NOT NULL,
    duration VARCHAR(50),
    prerequisites TEXT,
    objectives TEXT,
    program TEXT,
    career TEXT,
    status ENUM('brouillon', 'publié') DEFAULT 'brouillon',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    FOREIGN KEY (created_by) REFERENCES utilisateurs(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table des événements
CREATE TABLE IF NOT EXISTS evenements (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    date_evenement DATE NOT NULL,
    lieu VARCHAR(255),
    type ENUM('Salon', 'Conférence', 'Portes ouvertes', 'Sortie pédagogique', 'Concours', 'Cérémonie') DEFAULT 'Conférence',
    participants TEXT,
    contact VARCHAR(255),
    status ENUM('brouillon', 'publié') DEFAULT 'brouillon',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    FOREIGN KEY (created_by) REFERENCES utilisateurs(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table des emplois du temps (avec filière et groupe)
CREATE TABLE IF NOT EXISTS emplois_du_temps (
    id INT AUTO_INCREMENT PRIMARY KEY,
    filiere VARCHAR(100) NOT NULL COMMENT 'Ex: BTS ACSE, CAP Agriculture, Bac Pro CGEA',
    groupe VARCHAR(100) NOT NULL COMMENT 'Ex: 1ère année, 2ème année, Groupe A, Apprentissage',
    semaine VARCHAR(100) NOT NULL COMMENT 'Ex: Semaine du 18-22 Mars 2024',
    file_name VARCHAR(255),
    file_path VARCHAR(500),
    file_size INT UNSIGNED,
    description TEXT,
    status ENUM('brouillon', 'publié') DEFAULT 'brouillon',
    upload_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    FOREIGN KEY (created_by) REFERENCES utilisateurs(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table des sessions de connexion (pour la sécurité)
CREATE TABLE IF NOT EXISTS sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES utilisateurs(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Index pour améliorer les performances
CREATE INDEX idx_actualites_status ON actualites(status);
CREATE INDEX idx_actualites_category ON actualites(category);
CREATE INDEX idx_actualites_date ON actualites(date_publication);
CREATE INDEX idx_formations_level ON formations(level);
CREATE INDEX idx_formations_status ON formations(status);
CREATE INDEX idx_evenements_date ON evenements(date_evenement);
CREATE INDEX idx_evenements_status ON evenements(status);
CREATE INDEX idx_emplois_filiere ON emplois_du_temps(filiere);
CREATE INDEX idx_emplois_groupe ON emplois_du_temps(groupe);
CREATE INDEX idx_emplois_status ON emplois_du_temps(status);
CREATE INDEX idx_utilisateurs_email ON utilisateurs(email);
CREATE INDEX idx_utilisateurs_status ON utilisateurs(status);
CREATE INDEX idx_sessions_token ON sessions(session_token);
CREATE INDEX idx_sessions_expires ON sessions(expires_at);
