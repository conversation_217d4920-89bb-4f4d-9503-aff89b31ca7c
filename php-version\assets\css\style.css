/* 
 * CSS Principal - Centre de Formation Agricole CMC
 * Reproduction exacte du design React en CSS pur
 */

/* ========================================
   VARIABLES CSS (équivalent Tailwind)
======================================== */
:root {
  /* Couleurs */
  --color-primary: #16a34a;
  --color-primary-dark: #15803d;
  --color-primary-light: #22c55e;
  --color-secondary: #f59e0b;
  --color-accent: #3b82f6;
  
  /* Grays */
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;
  
  /* Spacing */
  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
  --spacing-3: 0.75rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-8: 2rem;
  --spacing-10: 2.5rem;
  --spacing-12: 3rem;
  --spacing-16: 4rem;
  --spacing-20: 5rem;
  --spacing-24: 6rem;
  
  /* Typography */
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  --font-size-5xl: 3rem;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  
  /* Border radius */
  --radius-sm: 0.125rem;
  --radius: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-full: 9999px;
  
  /* Transitions */
  --transition: all 0.15s ease-in-out;
  --transition-colors: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
}

/* ========================================
   RESET ET BASE
======================================== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  line-height: 1.6;
  color: var(--color-gray-900);
  background-color: white;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ========================================
   NAVIGATION
======================================== */
.navbar {
  background-color: white;
  border-bottom: 1px solid var(--color-gray-200);
  position: sticky;
  top: 0;
  z-index: 50;
  box-shadow: var(--shadow-sm);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 4rem;
}

.nav-logo a {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: var(--color-gray-900);
  font-weight: 700;
  font-size: var(--font-size-xl);
}

.logo-img {
  height: 2rem;
  width: 2rem;
  margin-right: var(--spacing-2);
}

.logo-text {
  color: var(--color-primary);
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: var(--spacing-8);
}

.nav-list {
  display: flex;
  list-style: none;
  gap: var(--spacing-6);
  margin: 0;
}

.nav-link {
  text-decoration: none;
  color: var(--color-gray-600);
  font-weight: 500;
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-md);
  transition: var(--transition-colors);
}

.nav-link:hover,
.nav-link.active {
  color: var(--color-primary);
  background-color: var(--color-gray-50);
}

.btn-admin {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2) var(--spacing-4);
  background-color: var(--color-primary);
  color: white;
  text-decoration: none;
  border-radius: var(--radius-md);
  font-weight: 500;
  font-size: var(--font-size-sm);
  transition: var(--transition);
}

.btn-admin:hover {
  background-color: var(--color-primary-dark);
  transform: translateY(-1px);
}

/* Menu mobile */
.nav-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
  gap: 3px;
}

.bar {
  width: 25px;
  height: 3px;
  background-color: var(--color-gray-600);
  transition: var(--transition);
}

/* ========================================
   LAYOUT PRINCIPAL
======================================== */
.main-content {
  min-height: calc(100vh - 4rem);
  padding-top: 0;
}

/* ========================================
   COMPOSANTS COMMUNS
======================================== */

/* Boutons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2) var(--spacing-4);
  border: none;
  border-radius: var(--radius-md);
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: var(--transition);
  font-size: var(--font-size-sm);
}

.btn-primary {
  background-color: var(--color-primary);
  color: white;
}

.btn-primary:hover {
  background-color: var(--color-primary-dark);
  transform: translateY(-1px);
}

.btn-secondary {
  background-color: var(--color-gray-100);
  color: var(--color-gray-700);
}

.btn-secondary:hover {
  background-color: var(--color-gray-200);
}

.btn-outline {
  background-color: transparent;
  color: var(--color-primary);
  border: 1px solid var(--color-primary);
}

.btn-outline:hover {
  background-color: var(--color-primary);
  color: white;
}

/* Cards */
.card {
  background-color: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow);
  overflow: hidden;
  transition: var(--transition);
}

.card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.card-header {
  padding: var(--spacing-6);
  border-bottom: 1px solid var(--color-gray-200);
}

.card-body {
  padding: var(--spacing-6);
}

.card-footer {
  padding: var(--spacing-6);
  border-top: 1px solid var(--color-gray-200);
  background-color: var(--color-gray-50);
}

/* Badges */
.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-1) var(--spacing-3);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: 500;
}

.badge-success {
  background-color: #dcfce7;
  color: #166534;
}

.badge-warning {
  background-color: #fef3c7;
  color: #92400e;
}

.badge-info {
  background-color: #dbeafe;
  color: #1e40af;
}

/* Containers */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

.section {
  padding: var(--spacing-16) 0;
}

/* Grid */
.grid {
  display: grid;
  gap: var(--spacing-6);
}

.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

/* Flex */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-2 { gap: var(--spacing-2); }
.gap-4 { gap: var(--spacing-4); }
.gap-6 { gap: var(--spacing-6); }
.gap-8 { gap: var(--spacing-8); }

/* Typography */
.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-base { font-size: var(--font-size-base); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }
.text-2xl { font-size: var(--font-size-2xl); }
.text-3xl { font-size: var(--font-size-3xl); }
.text-4xl { font-size: var(--font-size-4xl); }
.text-5xl { font-size: var(--font-size-5xl); }

.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

/* Colors */
.text-gray-500 { color: var(--color-gray-500); }
.text-gray-600 { color: var(--color-gray-600); }
.text-gray-700 { color: var(--color-gray-700); }
.text-gray-900 { color: var(--color-gray-900); }
.text-primary { color: var(--color-primary); }

/* Spacing */
.p-4 { padding: var(--spacing-4); }
.p-6 { padding: var(--spacing-6); }
.p-8 { padding: var(--spacing-8); }

.px-4 { padding-left: var(--spacing-4); padding-right: var(--spacing-4); }
.py-4 { padding-top: var(--spacing-4); padding-bottom: var(--spacing-4); }

.m-4 { margin: var(--spacing-4); }
.mb-4 { margin-bottom: var(--spacing-4); }
.mb-6 { margin-bottom: var(--spacing-6); }
.mb-8 { margin-bottom: var(--spacing-8); }

/* ========================================
   PAGES SPÉCIFIQUES
======================================== */

/* Hero Section */
.hero {
  position: relative;
  height: 100vh;
  min-height: 600px;
  display: flex;
  align-items: center;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(22, 163, 74, 0.8) 0%, rgba(21, 128, 61, 0.6) 100%);
  z-index: 1;
}

.hero-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.hero-content {
  position: relative;
  z-index: 2;
  width: 100%;
}

.hero-text {
  max-width: 600px;
  color: white;
}

.hero-title {
  font-size: var(--font-size-5xl);
  font-weight: 700;
  line-height: 1.1;
  margin-bottom: var(--spacing-6);
}

.hero-highlight {
  color: var(--color-secondary);
}

.hero-description {
  font-size: var(--font-size-xl);
  line-height: 1.6;
  margin-bottom: var(--spacing-8);
  opacity: 0.95;
}

.hero-actions {
  display: flex;
  gap: var(--spacing-4);
  flex-wrap: wrap;
}

.btn-lg {
  padding: var(--spacing-4) var(--spacing-6);
  font-size: var(--font-size-lg);
}

/* Stats Section */
.stats-section {
  background-color: var(--color-gray-50);
  padding: var(--spacing-16) 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-8);
}

.stat-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
  padding: var(--spacing-6);
  background-color: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow);
}

.stat-icon {
  width: 3rem;
  height: 3rem;
  background-color: var(--color-primary);
  color: white;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-number {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  color: var(--color-gray-900);
  line-height: 1;
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--color-gray-600);
  margin-top: var(--spacing-1);
}

/* Section Headers */
.section-header {
  text-align: center;
  margin-bottom: var(--spacing-12);
}

.section-title {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  color: var(--color-gray-900);
  margin-bottom: var(--spacing-4);
}

.section-description {
  font-size: var(--font-size-lg);
  color: var(--color-gray-600);
  max-width: 600px;
  margin: 0 auto;
}

.section-footer {
  text-align: center;
  margin-top: var(--spacing-12);
}

/* Formations Grid */
.formations-section {
  background-color: white;
}

.formations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-8);
}

.formation-card {
  background-color: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow);
  overflow: hidden;
  transition: var(--transition);
  border: 1px solid var(--color-gray-200);
}

.formation-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-4px);
}

.formation-header {
  padding: var(--spacing-6);
  border-bottom: 1px solid var(--color-gray-200);
}

.formation-level {
  margin-bottom: var(--spacing-3);
}

.formation-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--color-gray-900);
  line-height: 1.3;
}

.formation-content {
  padding: var(--spacing-6);
}

.formation-description {
  color: var(--color-gray-600);
  line-height: 1.6;
  margin-bottom: var(--spacing-4);
}

.formation-details {
  display: flex;
  gap: var(--spacing-4);
}

.formation-detail {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  color: var(--color-gray-500);
  font-size: var(--font-size-sm);
}

.formation-footer {
  padding: var(--spacing-6);
  border-top: 1px solid var(--color-gray-200);
  background-color: var(--color-gray-50);
}

/* Actualités Grid */
.actualites-section {
  background-color: var(--color-gray-50);
}

.actualites-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-8);
}

.actualite-card {
  background-color: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow);
  overflow: hidden;
  transition: var(--transition);
}

.actualite-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.actualite-image {
  height: 200px;
  overflow: hidden;
}

.actualite-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition);
}

.actualite-card:hover .actualite-image img {
  transform: scale(1.05);
}

.actualite-content {
  padding: var(--spacing-6);
}

.actualite-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-4);
}

.actualite-date {
  font-size: var(--font-size-sm);
  color: var(--color-gray-500);
}

.actualite-title a {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--color-gray-900);
  text-decoration: none;
  line-height: 1.3;
}

.actualite-title a:hover {
  color: var(--color-primary);
}

.actualite-excerpt {
  color: var(--color-gray-600);
  line-height: 1.6;
  margin: var(--spacing-4) 0;
}

.actualite-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: var(--spacing-4);
  border-top: 1px solid var(--color-gray-200);
}

.actualite-author {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-size: var(--font-size-sm);
  color: var(--color-gray-500);
}

.actualite-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  color: var(--color-primary);
  text-decoration: none;
  font-weight: 500;
  font-size: var(--font-size-sm);
}

.actualite-link:hover {
  color: var(--color-primary-dark);
}

/* Événements Grid */
.evenements-section {
  background-color: white;
}

.evenements-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-8);
}

.evenement-card {
  background-color: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow);
  padding: var(--spacing-6);
  border: 1px solid var(--color-gray-200);
  transition: var(--transition);
}

.evenement-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.evenement-date {
  background-color: var(--color-primary);
  color: white;
  border-radius: var(--radius-lg);
  padding: var(--spacing-4);
  text-align: center;
  margin-bottom: var(--spacing-4);
  width: fit-content;
}

.date-day {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  line-height: 1;
}

.date-month {
  font-size: var(--font-size-sm);
  text-transform: uppercase;
  opacity: 0.9;
}

.evenement-meta {
  margin-bottom: var(--spacing-3);
}

.evenement-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--color-gray-900);
  margin-bottom: var(--spacing-4);
  line-height: 1.3;
}

.evenement-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-4);
}

.evenement-detail {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  color: var(--color-gray-500);
  font-size: var(--font-size-sm);
}

.evenement-description {
  color: var(--color-gray-600);
  line-height: 1.6;
  margin-bottom: var(--spacing-6);
}

.evenement-footer {
  border-top: 1px solid var(--color-gray-200);
  padding-top: var(--spacing-4);
}

.btn-sm {
  padding: var(--spacing-2) var(--spacing-4);
  font-size: var(--font-size-sm);
}

/* CTA Section */
.cta-section {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
  color: white;
  padding: var(--spacing-20) 0;
}

.cta-content {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.cta-title {
  font-size: var(--font-size-4xl);
  font-weight: 700;
  margin-bottom: var(--spacing-6);
}

.cta-description {
  font-size: var(--font-size-lg);
  line-height: 1.6;
  margin-bottom: var(--spacing-8);
  opacity: 0.95;
}

.cta-actions {
  display: flex;
  gap: var(--spacing-4);
  justify-content: center;
  flex-wrap: wrap;
}

.cta-section .btn-outline {
  background-color: transparent;
  color: white;
  border-color: white;
}

.cta-section .btn-outline:hover {
  background-color: white;
  color: var(--color-primary);
}

/* ========================================
   RESPONSIVE
======================================== */
@media (max-width: 768px) {
  .nav-menu {
    position: fixed;
    top: 4rem;
    left: -100%;
    width: 100%;
    height: calc(100vh - 4rem);
    background-color: white;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    padding: var(--spacing-8);
    transition: left 0.3s ease;
    box-shadow: var(--shadow-lg);
  }
  
  .nav-menu.active {
    left: 0;
  }
  
  .nav-list {
    flex-direction: column;
    gap: var(--spacing-4);
    width: 100%;
  }
  
  .nav-link {
    display: block;
    width: 100%;
    text-align: center;
    padding: var(--spacing-4);
  }
  
  .nav-toggle {
    display: flex;
  }
  
  .nav-toggle.active .bar:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
  }
  
  .nav-toggle.active .bar:nth-child(2) {
    opacity: 0;
  }
  
  .nav-toggle.active .bar:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
  }
  
  .grid-cols-2,
  .grid-cols-3,
  .grid-cols-4 {
    grid-template-columns: 1fr;
  }
  
  .container {
    padding: 0 var(--spacing-3);
  }
}
