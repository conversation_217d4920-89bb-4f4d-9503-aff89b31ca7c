// Services pour les autres entités de la base de données
import { getStorageData, setStorageData, STORAGE_KEYS } from './database.js';

// Fonctions utilitaires pour les formations
export const FormationService = {
  getAll: () => {
    const formations = getStorageData(STORAGE_KEYS.FORMATIONS);
    const users = getStorageData(STORAGE_KEYS.USERS);
    
    return formations.map(formation => {
      const user = users.find(u => u.id === formation.created_by);
      return {
        ...formation,
        created_by_name: user ? `${user.prenom} ${user.nom}` : 'Inconnu'
      };
    }).sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
  },

  create: (data, userId) => {
    const formations = getStorageData(STORAGE_KEYS.FORMATIONS);
    const newId = Math.max(...formations.map(f => f.id), 0) + 1;
    
    const newFormation = {
      id: newId,
      title: data.title,
      description: data.description,
      level: data.level,
      duration: data.duration || '',
      prerequisites: data.prerequisites || '',
      objectives: data.objectives || '',
      program: data.program || '',
      career: data.career || '',
      status: data.status || 'brouillon',
      created_by: userId,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    
    formations.push(newFormation);
    setStorageData(STORAGE_KEYS.FORMATIONS, formations);
    return { insertId: newId };
  },

  update: (id, data) => {
    const formations = getStorageData(STORAGE_KEYS.FORMATIONS);
    const formationIndex = formations.findIndex(f => f.id === parseInt(id));
    
    if (formationIndex !== -1) {
      formations[formationIndex] = {
        ...formations[formationIndex],
        title: data.title,
        description: data.description,
        level: data.level,
        duration: data.duration,
        prerequisites: data.prerequisites,
        objectives: data.objectives,
        program: data.program,
        career: data.career,
        status: data.status,
        updated_at: new Date().toISOString()
      };
      
      setStorageData(STORAGE_KEYS.FORMATIONS, formations);
      return { changes: 1 };
    }
    
    return { changes: 0 };
  },

  delete: (id) => {
    const formations = getStorageData(STORAGE_KEYS.FORMATIONS);
    const filteredFormations = formations.filter(f => f.id !== parseInt(id));
    
    if (filteredFormations.length < formations.length) {
      setStorageData(STORAGE_KEYS.FORMATIONS, filteredFormations);
      return { changes: 1 };
    }
    
    return { changes: 0 };
  }
};

// Fonctions utilitaires pour les événements
export const EvenementService = {
  getAll: () => {
    const evenements = getStorageData(STORAGE_KEYS.EVENEMENTS);
    const users = getStorageData(STORAGE_KEYS.USERS);
    
    return evenements.map(evenement => {
      const user = users.find(u => u.id === evenement.created_by);
      return {
        ...evenement,
        created_by_name: user ? `${user.prenom} ${user.nom}` : 'Inconnu'
      };
    }).sort((a, b) => new Date(b.date_evenement) - new Date(a.date_evenement));
  },

  create: (data, userId) => {
    const evenements = getStorageData(STORAGE_KEYS.EVENEMENTS);
    const newId = Math.max(...evenements.map(e => e.id), 0) + 1;
    
    const newEvenement = {
      id: newId,
      title: data.title,
      description: data.description,
      date_evenement: data.date,
      lieu: data.lieu || '',
      type: data.type || 'Conférence',
      participants: data.participants || '',
      contact: data.contact || '',
      status: data.status || 'brouillon',
      created_by: userId,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    
    evenements.push(newEvenement);
    setStorageData(STORAGE_KEYS.EVENEMENTS, evenements);
    return { insertId: newId };
  },

  update: (id, data) => {
    const evenements = getStorageData(STORAGE_KEYS.EVENEMENTS);
    const evenementIndex = evenements.findIndex(e => e.id === parseInt(id));
    
    if (evenementIndex !== -1) {
      evenements[evenementIndex] = {
        ...evenements[evenementIndex],
        title: data.title,
        description: data.description,
        date_evenement: data.date,
        lieu: data.lieu,
        type: data.type,
        participants: data.participants,
        contact: data.contact,
        status: data.status,
        updated_at: new Date().toISOString()
      };
      
      setStorageData(STORAGE_KEYS.EVENEMENTS, evenements);
      return { changes: 1 };
    }
    
    return { changes: 0 };
  },

  delete: (id) => {
    const evenements = getStorageData(STORAGE_KEYS.EVENEMENTS);
    const filteredEvenements = evenements.filter(e => e.id !== parseInt(id));
    
    if (filteredEvenements.length < evenements.length) {
      setStorageData(STORAGE_KEYS.EVENEMENTS, filteredEvenements);
      return { changes: 1 };
    }
    
    return { changes: 0 };
  }
};

// Fonctions utilitaires pour les emplois du temps
export const EmploiDuTempsService = {
  getAll: () => {
    const emplois = getStorageData(STORAGE_KEYS.EMPLOIS);
    const users = getStorageData(STORAGE_KEYS.USERS);
    
    return emplois.map(emploi => {
      const user = users.find(u => u.id === emploi.created_by);
      return {
        ...emploi,
        created_by_name: user ? `${user.prenom} ${user.nom}` : 'Inconnu'
      };
    }).sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
  },

  create: (data, userId) => {
    const emplois = getStorageData(STORAGE_KEYS.EMPLOIS);
    const newId = Math.max(...emplois.map(e => e.id), 0) + 1;
    
    const newEmploi = {
      id: newId,
      classe: data.classe,
      semaine: data.semaine,
      file_name: data.fileName || null,
      file_path: data.fileUrl || null,
      file_size: data.fileSize || null,
      description: data.description || '',
      status: data.status || 'brouillon',
      upload_date: new Date().toISOString().split('T')[0],
      created_by: userId,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    
    emplois.push(newEmploi);
    setStorageData(STORAGE_KEYS.EMPLOIS, emplois);
    return { insertId: newId };
  },

  update: (id, data) => {
    const emplois = getStorageData(STORAGE_KEYS.EMPLOIS);
    const emploiIndex = emplois.findIndex(e => e.id === parseInt(id));
    
    if (emploiIndex !== -1) {
      emplois[emploiIndex] = {
        ...emplois[emploiIndex],
        classe: data.classe,
        semaine: data.semaine,
        description: data.description,
        status: data.status,
        updated_at: new Date().toISOString()
      };
      
      setStorageData(STORAGE_KEYS.EMPLOIS, emplois);
      return { changes: 1 };
    }
    
    return { changes: 0 };
  },

  delete: (id) => {
    const emplois = getStorageData(STORAGE_KEYS.EMPLOIS);
    const filteredEmplois = emplois.filter(e => e.id !== parseInt(id));
    
    if (filteredEmplois.length < emplois.length) {
      setStorageData(STORAGE_KEYS.EMPLOIS, filteredEmplois);
      return { changes: 1 };
    }
    
    return { changes: 0 };
  }
};

// Fonctions utilitaires pour les sessions
export const SessionService = {
  create: (userId) => {
    const sessions = getStorageData(STORAGE_KEYS.SESSIONS);
    const sessionToken = crypto.randomUUID();
    const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 heures
    
    const newSession = {
      id: Math.max(...sessions.map(s => s.id), 0) + 1,
      user_id: userId,
      session_token: sessionToken,
      expires_at: expiresAt.toISOString(),
      created_at: new Date().toISOString()
    };
    
    sessions.push(newSession);
    setStorageData(STORAGE_KEYS.SESSIONS, sessions);
    return sessionToken;
  },

  validate: (sessionToken) => {
    const sessions = getStorageData(STORAGE_KEYS.SESSIONS);
    const users = getStorageData(STORAGE_KEYS.USERS);
    
    const session = sessions.find(s => 
      s.session_token === sessionToken && 
      new Date(s.expires_at) > new Date()
    );
    
    if (session) {
      const user = users.find(u => u.id === session.user_id && u.status === 'actif');
      if (user) {
        const { password_hash, ...userWithoutPassword } = user;
        return { ...session, ...userWithoutPassword };
      }
    }
    
    return null;
  },

  delete: (sessionToken) => {
    const sessions = getStorageData(STORAGE_KEYS.SESSIONS);
    const filteredSessions = sessions.filter(s => s.session_token !== sessionToken);
    
    if (filteredSessions.length < sessions.length) {
      setStorageData(STORAGE_KEYS.SESSIONS, filteredSessions);
      return { changes: 1 };
    }
    
    return { changes: 0 };
  },

  cleanup: () => {
    const sessions = getStorageData(STORAGE_KEYS.SESSIONS);
    const now = new Date();
    const validSessions = sessions.filter(s => new Date(s.expires_at) > now);
    
    if (validSessions.length < sessions.length) {
      setStorageData(STORAGE_KEYS.SESSIONS, validSessions);
      return { changes: sessions.length - validSessions.length };
    }
    
    return { changes: 0 };
  }
};
