# 🌱 Site Web CMC - Version PHP

## 📋 Description

Version PHP/HTML/CSS du site web du Centre de Formation Agricole CMC. Cette version reproduit exactement le même design et les mêmes fonctionnalités que la version React, mais utilise des technologies web traditionnelles.

## 🚀 Fonctionnalités

### 🎯 **Site Public**
- ✅ Page d'accueil avec hero section
- ✅ Section formations avec détails
- ✅ Actualités et événements
- ✅ Emplois du temps par filière/groupe
- ✅ Page de contact
- ✅ Design responsive identique
- ✅ Navigation fluide
- ✅ Animations et interactions

### 🔐 **Interface d'Administration**
- ✅ Connexion sécurisée
- ✅ Tableau de bord avec statistiques
- ✅ Gestion des actualités (CRUD)
- ✅ Gestion des formations (CRUD)
- ✅ Gestion des événements (CRUD)
- ✅ Gestion des emplois du temps (CRUD)
- ✅ Gestion des utilisateurs
- ✅ Interface identique à React

## 🛠️ Technologies Utilisées

- **Backend** : PHP 7.4+
- **Base de données** : MySQL/MariaDB
- **Frontend** : HTML5, CSS3, JavaScript Vanilla
- **Icons** : Lucide Icons
- **Fonts** : Inter (Google Fonts)

## 📦 Installation

### **Prérequis**
- Serveur web (Apache/Nginx)
- PHP 7.4 ou supérieur
- MySQL 5.7+ ou MariaDB 10.3+
- phpMyAdmin (recommandé)

### **Étape 1 : Configuration du serveur**

1. **XAMPP/WAMP/MAMP** :
   ```bash
   # Placer le dossier php-version dans htdocs/www
   C:\xampp\htdocs\cmc-php\
   ```

2. **Serveur Linux** :
   ```bash
   # Placer dans /var/www/html/
   sudo cp -r php-version /var/www/html/cmc-php
   sudo chown -R www-data:www-data /var/www/html/cmc-php
   ```

### **Étape 2 : Base de données**

1. **Créer la base de données** :
   ```sql
   CREATE DATABASE cmc_database CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

2. **Exécuter le schéma** :
   ```bash
   # Dans phpMyAdmin, exécuter le fichier :
   database/schema-mysql.sql
   ```

3. **Insérer les données** :
   ```bash
   # Dans phpMyAdmin, exécuter le fichier :
   database/seed-mysql-fixed.sql
   ```

### **Étape 3 : Configuration**

1. **Modifier la configuration** :
   ```php
   // php-version/config/database.php
   define('DB_HOST', 'localhost');
   define('DB_NAME', 'cmc_database');
   define('DB_USER', 'votre_utilisateur');
   define('DB_PASS', 'votre_mot_de_passe');
   ```

2. **Permissions des fichiers** :
   ```bash
   chmod 755 php-version/
   chmod 644 php-version/config/database.php
   ```

## 🌐 Accès au Site

### **Site Public**
```
http://localhost/cmc-php/
```

### **Administration**
```
http://localhost/cmc-php/admin/
```

**Comptes de test** :
| Email | Mot de passe | Rôle |
|-------|-------------|------|
| <EMAIL> | admin123 | Administrateur principal |
| <EMAIL> | admin123 | Administrateur |
| <EMAIL> | admin123 | Lecture seule |

## 📁 Structure du Projet

```
php-version/
├── assets/
│   ├── css/
│   │   ├── style.css          # Styles principaux
│   │   └── admin.css          # Styles administration
│   ├── js/
│   │   ├── main.js            # JavaScript principal
│   │   └── admin.js           # JavaScript admin
│   └── images/                # Images du site
├── admin/
│   ├── includes/
│   │   ├── admin_header.php   # En-tête admin
│   │   └── admin_footer.php   # Pied de page admin
│   ├── login.php              # Connexion admin
│   ├── dashboard.php          # Tableau de bord
│   ├── actualites.php         # Gestion actualités
│   ├── formations.php         # Gestion formations
│   ├── evenements.php         # Gestion événements
│   ├── emplois-du-temps.php   # Gestion emplois du temps
│   ├── utilisateurs.php       # Gestion utilisateurs
│   └── logout.php             # Déconnexion
├── config/
│   └── database.php           # Configuration BDD
├── includes/
│   ├── header.php             # En-tête site
│   └── footer.php             # Pied de page site
├── pages/
│   ├── formations.php         # Page formations
│   ├── actualites.php         # Page actualités
│   ├── evenements.php         # Page événements
│   ├── emplois-du-temps.php   # Page emplois du temps
│   └── contact.php            # Page contact
├── index.php                  # Page d'accueil
└── README.md                  # Ce fichier
```

## 🎨 Design et Styles

### **Reproduction Exacte**
- ✅ Même palette de couleurs
- ✅ Même typographie (Inter)
- ✅ Mêmes espacements et grilles
- ✅ Mêmes animations et transitions
- ✅ Même responsive design
- ✅ Mêmes icônes (Lucide)

### **Variables CSS**
```css
:root {
  --color-primary: #16a34a;
  --color-secondary: #f59e0b;
  --font-family: 'Inter', sans-serif;
  /* ... autres variables */
}
```

## 🔧 Fonctionnalités Techniques

### **Base de Données**
- Connexion PDO sécurisée
- Requêtes préparées
- Gestion des erreurs
- Transactions

### **Sécurité**
- Protection CSRF
- Validation des données
- Échappement HTML
- Sessions sécurisées

### **Performance**
- CSS optimisé
- JavaScript modulaire
- Images optimisées
- Cache navigateur

## 🚀 Déploiement

### **Serveur de Production**

1. **Upload des fichiers** :
   ```bash
   rsync -av php-version/ user@server:/var/www/html/
   ```

2. **Configuration Apache** :
   ```apache
   <VirtualHost *:80>
       ServerName cmc-formation.fr
       DocumentRoot /var/www/html/php-version
       DirectoryIndex index.php
   </VirtualHost>
   ```

3. **SSL/HTTPS** :
   ```bash
   certbot --apache -d cmc-formation.fr
   ```

## 🔍 Maintenance

### **Sauvegarde**
```bash
# Base de données
mysqldump -u user -p cmc_database > backup.sql

# Fichiers
tar -czf backup.tar.gz php-version/
```

### **Mise à jour**
```bash
# Sauvegarder d'abord
# Puis remplacer les fichiers
# Vérifier la configuration
```

## 🐛 Dépannage

### **Erreurs Courantes**

1. **Erreur de connexion BDD** :
   - Vérifier les identifiants dans `config/database.php`
   - Vérifier que MySQL est démarré

2. **Page blanche** :
   - Activer l'affichage des erreurs PHP
   - Vérifier les logs d'erreur

3. **Styles non chargés** :
   - Vérifier les chemins des fichiers CSS
   - Vérifier les permissions

## 📞 Support

Pour toute question ou problème :
- Vérifier la documentation
- Consulter les logs d'erreur
- Tester avec les comptes de démonstration

## 🎉 Résultat

Vous obtenez exactement le même site que la version React, mais en PHP/HTML/CSS traditionnel, plus facile à héberger et maintenir sur la plupart des serveurs web ! 🌱
