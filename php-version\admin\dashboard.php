<?php
session_start();
require_once '../config/database.php';

// Vérifier l'authentification
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: login.php');
    exit;
}

$page_title = 'Tableau de bord';
$db = getDB();

// Récupérer les statistiques
$stats = [
    'actualites' => $db->selectOne("SELECT COUNT(*) as count FROM actualites")['count'],
    'formations' => $db->selectOne("SELECT COUNT(*) as count FROM formations")['count'],
    'evenements' => $db->selectOne("SELECT COUNT(*) as count FROM evenements")['count'],
    'emplois_du_temps' => $db->selectOne("SELECT COUNT(*) as count FROM emplois_du_temps")['count'],
    'utilisateurs' => $db->selectOne("SELECT COUNT(*) as count FROM utilisateurs")['count']
];

// Récupérer les dernières actualités
$recent_actualites = $db->select(
    "SELECT * FROM actualites ORDER BY created_at DESC LIMIT 5"
);

// Récupérer les prochains événements
$upcoming_events = $db->select(
    "SELECT * FROM evenements WHERE date_evenement >= CURDATE() ORDER BY date_evenement ASC LIMIT 5"
);

include 'includes/admin_header.php';
?>

<div class="dashboard-content">
    <!-- Statistiques -->
    <div class="stats-overview">
        <h2 class="section-title">Vue d'ensemble</h2>
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon stat-icon-blue">
                    <i data-lucide="newspaper"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><?php echo $stats['actualites']; ?></div>
                    <div class="stat-label">Actualités</div>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon stat-icon-green">
                    <i data-lucide="graduation-cap"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><?php echo $stats['formations']; ?></div>
                    <div class="stat-label">Formations</div>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon stat-icon-yellow">
                    <i data-lucide="calendar"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><?php echo $stats['evenements']; ?></div>
                    <div class="stat-label">Événements</div>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon stat-icon-purple">
                    <i data-lucide="clock"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><?php echo $stats['emplois_du_temps']; ?></div>
                    <div class="stat-label">Emplois du temps</div>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon stat-icon-red">
                    <i data-lucide="users"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><?php echo $stats['utilisateurs']; ?></div>
                    <div class="stat-label">Utilisateurs</div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Actions rapides -->
    <div class="quick-actions">
        <h2 class="section-title">Actions rapides</h2>
        <div class="actions-grid">
            <a href="actualites.php" class="action-card">
                <div class="action-icon">
                    <i data-lucide="plus"></i>
                </div>
                <div class="action-content">
                    <h3>Nouvelle actualité</h3>
                    <p>Publier une nouvelle actualité</p>
                </div>
            </a>
            
            <a href="formations.php" class="action-card">
                <div class="action-icon">
                    <i data-lucide="book-open"></i>
                </div>
                <div class="action-content">
                    <h3>Ajouter une formation</h3>
                    <p>Créer une nouvelle formation</p>
                </div>
            </a>
            
            <a href="evenements.php" class="action-card">
                <div class="action-icon">
                    <i data-lucide="calendar-plus"></i>
                </div>
                <div class="action-content">
                    <h3>Nouvel événement</h3>
                    <p>Organiser un événement</p>
                </div>
            </a>
            
            <a href="emplois-du-temps.php" class="action-card">
                <div class="action-icon">
                    <i data-lucide="upload"></i>
                </div>
                <div class="action-content">
                    <h3>Emploi du temps</h3>
                    <p>Importer un planning</p>
                </div>
            </a>
        </div>
    </div>
    
    <!-- Contenu récent -->
    <div class="dashboard-grid">
        <!-- Dernières actualités -->
        <div class="dashboard-section">
            <div class="section-header">
                <h2 class="section-title">Dernières actualités</h2>
                <a href="actualites.php" class="btn btn-outline btn-sm">
                    Voir tout
                    <i data-lucide="arrow-right"></i>
                </a>
            </div>
            
            <div class="content-list">
                <?php if (empty($recent_actualites)): ?>
                <div class="empty-state">
                    <i data-lucide="newspaper"></i>
                    <p>Aucune actualité pour le moment</p>
                    <a href="actualites.php" class="btn btn-primary btn-sm">Créer une actualité</a>
                </div>
                <?php else: ?>
                <?php foreach ($recent_actualites as $actualite): ?>
                <div class="content-item">
                    <div class="content-meta">
                        <span class="badge badge-<?php echo $actualite['status'] == 'publié' ? 'success' : 'warning'; ?>">
                            <?php echo ucfirst($actualite['status']); ?>
                        </span>
                        <span class="content-date">
                            <?php echo date('d/m/Y', strtotime($actualite['created_at'])); ?>
                        </span>
                    </div>
                    <h3 class="content-title">
                        <a href="actualites.php?edit=<?php echo $actualite['id']; ?>">
                            <?php echo htmlspecialchars($actualite['title']); ?>
                        </a>
                    </h3>
                    <p class="content-excerpt">
                        <?php echo htmlspecialchars(substr($actualite['content'], 0, 100)) . '...'; ?>
                    </p>
                    <div class="content-actions">
                        <a href="actualites.php?edit=<?php echo $actualite['id']; ?>" class="btn btn-outline btn-xs">
                            <i data-lucide="edit"></i>
                            Modifier
                        </a>
                    </div>
                </div>
                <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Prochains événements -->
        <div class="dashboard-section">
            <div class="section-header">
                <h2 class="section-title">Prochains événements</h2>
                <a href="evenements.php" class="btn btn-outline btn-sm">
                    Voir tout
                    <i data-lucide="arrow-right"></i>
                </a>
            </div>
            
            <div class="content-list">
                <?php if (empty($upcoming_events)): ?>
                <div class="empty-state">
                    <i data-lucide="calendar"></i>
                    <p>Aucun événement à venir</p>
                    <a href="evenements.php" class="btn btn-primary btn-sm">Créer un événement</a>
                </div>
                <?php else: ?>
                <?php foreach ($upcoming_events as $event): ?>
                <div class="content-item">
                    <div class="content-meta">
                        <span class="badge badge-info"><?php echo htmlspecialchars($event['type']); ?></span>
                        <span class="content-date">
                            <?php echo date('d/m/Y', strtotime($event['date_evenement'])); ?>
                        </span>
                    </div>
                    <h3 class="content-title">
                        <a href="evenements.php?edit=<?php echo $event['id']; ?>">
                            <?php echo htmlspecialchars($event['title']); ?>
                        </a>
                    </h3>
                    <p class="content-excerpt">
                        <i data-lucide="map-pin"></i>
                        <?php echo htmlspecialchars($event['lieu']); ?>
                    </p>
                    <div class="content-actions">
                        <a href="evenements.php?edit=<?php echo $event['id']; ?>" class="btn btn-outline btn-xs">
                            <i data-lucide="edit"></i>
                            Modifier
                        </a>
                    </div>
                </div>
                <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Activité récente -->
    <div class="activity-section">
        <h2 class="section-title">Activité récente</h2>
        <div class="activity-feed">
            <div class="activity-item">
                <div class="activity-icon activity-icon-create">
                    <i data-lucide="plus"></i>
                </div>
                <div class="activity-content">
                    <p><strong><?php echo htmlspecialchars($_SESSION['admin_user_name']); ?></strong> s'est connecté</p>
                    <span class="activity-time">Il y a quelques instants</span>
                </div>
            </div>
            
            <?php if (!empty($recent_actualites)): ?>
            <div class="activity-item">
                <div class="activity-icon activity-icon-edit">
                    <i data-lucide="edit"></i>
                </div>
                <div class="activity-content">
                    <p>Dernière actualité : <strong><?php echo htmlspecialchars($recent_actualites[0]['title']); ?></strong></p>
                    <span class="activity-time"><?php echo date('d/m/Y à H:i', strtotime($recent_actualites[0]['created_at'])); ?></span>
                </div>
            </div>
            <?php endif; ?>
            
            <div class="activity-item">
                <div class="activity-icon activity-icon-info">
                    <i data-lucide="info"></i>
                </div>
                <div class="activity-content">
                    <p>Base de données synchronisée</p>
                    <span class="activity-time">Aujourd'hui</span>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/admin_footer.php'; ?>
