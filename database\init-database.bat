@echo off
echo 🚀 Initialisation de la base de données CMC...

REM Vérifier si SQLite3 est installé
sqlite3 -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ SQLite3 n'est pas installé ou pas dans le PATH
    echo 📥 Téléchargez SQLite3 depuis: https://www.sqlite.org/download.html
    pause
    exit /b 1
)

echo ✅ SQLite3 détecté

REM Supprimer l'ancienne base de données si elle existe
if exist "database\cmc_database.sqlite" (
    del "database\cmc_database.sqlite"
    echo ✅ Ancienne base de données supprimée
)

REM Créer la base de données et exécuter le schéma
echo 📋 Création des tables...
sqlite3 "database\cmc_database.sqlite" ".read database\schema.sql"
if %errorlevel% neq 0 (
    echo ❌ Erreur lors de la création des tables
    pause
    exit /b 1
)
echo ✅ Tables créées avec succès

REM Insérer les données d'exemple
echo 📊 Insertion des données d'exemple...
sqlite3 "database\cmc_database.sqlite" ".read database\seed.sql"
if %errorlevel% neq 0 (
    echo ❌ Erreur lors de l'insertion des données
    pause
    exit /b 1
)
echo ✅ Données d'exemple insérées

REM Vérifier les données insérées
echo.
echo 📈 Vérification des données insérées:
sqlite3 "database\cmc_database.sqlite" "SELECT 'utilisateurs: ' || COUNT(*) FROM utilisateurs;"
sqlite3 "database\cmc_database.sqlite" "SELECT 'actualites: ' || COUNT(*) FROM actualites;"
sqlite3 "database\cmc_database.sqlite" "SELECT 'formations: ' || COUNT(*) FROM formations;"
sqlite3 "database\cmc_database.sqlite" "SELECT 'evenements: ' || COUNT(*) FROM evenements;"
sqlite3 "database\cmc_database.sqlite" "SELECT 'emplois_du_temps: ' || COUNT(*) FROM emplois_du_temps;"

echo.
echo 🎉 Base de données initialisée avec succès !
echo 📁 Fichier de base de données: database\cmc_database.sqlite
echo.
echo 📝 Informations de connexion par défaut:
echo    Email: <EMAIL>
echo    Mot de passe: admin123
echo.
echo ⚠️  N'oubliez pas de changer les mots de passe par défaut !
echo.
pause
