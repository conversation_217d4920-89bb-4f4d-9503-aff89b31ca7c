import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { Plus, PenSquare, Trash2, Calendar, Clock, Users, BookOpen, Image, FileText, Upload, Download, UserPlus, Mail, Shield } from "lucide-react";

// Import des services de base de données
import {
  initDatabase,
  UserService,
  ActualiteService,
  FormationService,
  EvenementService,
  EmploiDuTempsService
} from "../lib/database.js";
import { initAllData, resetAllData } from "../lib/init-data.js";

const Admin = () => {
  const { toast } = useToast();
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [loginData, setLoginData] = useState({
    email: "",
    password: ""
  });
  const [activeTab, setActiveTab] = useState("actualites");
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [editingItem, setEditingItem] = useState(null);
  const [isFileImport, setIsFileImport] = useState(false);
  const [selectedFile, setSelectedFile] = useState(null);
  const [isDragOver, setIsDragOver] = useState(false);

  // États pour les données de la base de données
  const [actualites, setActualites] = useState([]);
  const [formations, setFormations] = useState([]);
  const [evenements, setEvenements] = useState([]);
  const [emploisDuTemps, setEmploisDuTemps] = useState([]);
  const [utilisateurs, setUtilisateurs] = useState([]);
  const [loading, setLoading] = useState(true);

  // Charger les données au montage du composant
  useEffect(() => {
    const loadData = async () => {
      try {
        // Initialiser la base de données et les données d'exemple
        initDatabase();

        // Vérifier si les emplois du temps ont les nouveaux champs
        const currentEmplois = EmploiDuTempsService.getAll();
        if (currentEmplois.length > 0 && !currentEmplois[0].filiere) {
          // Réinitialiser les données pour inclure les nouveaux champs
          resetAllData();
        } else {
          initAllData();
        }

        // Charger toutes les données
        const actualitesData = ActualiteService.getAll();
        const formationsData = FormationService.getAll();
        const evenementsData = EvenementService.getAll();
        const emploisData = EmploiDuTempsService.getAll();
        const utilisateursData = UserService.getAll();

        setActualites(actualitesData);
        setFormations(formationsData);
        setEvenements(evenementsData);
        setEmploisDuTemps(emploisData);
        setUtilisateurs(utilisateursData);

        setLoading(false);
        console.log('✅ Données chargées depuis la base de données');
      } catch (error) {
        console.error('❌ Erreur lors du chargement des données:', error);
        setLoading(false);
      }
    };

    loadData();
  }, []);

  const handleLogin = (e) => {
    e.preventDefault();
    // Vérification des identifiants (à implémenter avec le backend)
    setIsLoggedIn(true);
    toast({
      title: "Connexion réussie",
      description: "Bienvenue dans l'interface d'administration."
    });
  };

  const handleLogout = () => {
    setIsLoggedIn(false);
    setLoginData({ email: "", password: "" });
  };

  const handleAdd = (isImport = false) => {
    setEditingItem(null);
    setIsFileImport(isImport);
    setIsAddDialogOpen(true);
  };

  const handleEdit = (item) => {
    setEditingItem(item);
    setIsAddDialogOpen(true);
  };

  const handleDelete = (id) => {
    try {
      // Logique de suppression selon le type de contenu
      if (activeTab === "actualites") {
        ActualiteService.delete(id);
        setActualites(ActualiteService.getAll());
      } else if (activeTab === "formations") {
        FormationService.delete(id);
        setFormations(FormationService.getAll());
      } else if (activeTab === "evenements") {
        EvenementService.delete(id);
        setEvenements(EvenementService.getAll());
      } else if (activeTab === "emplois") {
        EmploiDuTempsService.delete(id);
        setEmploisDuTemps(EmploiDuTempsService.getAll());
      } else if (activeTab === "utilisateurs") {
        UserService.delete(id);
        setUtilisateurs(UserService.getAll());
      }

      toast({
        title: "Élément supprimé",
        description: "L'élément a été supprimé avec succès."
      });
    } catch (error) {
      console.error('Erreur lors de la suppression:', error);
      toast({
        title: "Erreur",
        description: "Impossible de supprimer l'élément.",
        variant: "destructive"
      });
    }
  };

  const handleSave = (e) => {
    e.preventDefault();
    const formData = new FormData(e.target);
    const data = Object.fromEntries(formData.entries());

    try {
      if (activeTab === "actualites") {
        const actualiteData = {
          title: data.title,
          content: data.content,
          category: data.category || "formation",
          status: data.status || "brouillon",
          image_url: data.image || "",
          author: data.author || "Admin",
          date_publication: new Date().toISOString().split('T')[0]
        };

        if (editingItem) {
          ActualiteService.update(editingItem.id, actualiteData);
        } else {
          ActualiteService.create(actualiteData, 1); // TODO: Utiliser l'ID de l'utilisateur connecté
        }
        setActualites(ActualiteService.getAll());

      } else if (activeTab === "formations") {
        const formationData = {
          title: data.title,
          description: data.description,
          level: data.level || "",
          duration: data.duration || "",
          prerequisites: data.prerequisites || "",
          objectives: data.objectives || "",
          program: data.program || "",
          career: data.career || "",
          status: data.status || "brouillon"
        };

        if (editingItem) {
          FormationService.update(editingItem.id, formationData);
        } else {
          FormationService.create(formationData, 1); // TODO: Utiliser l'ID de l'utilisateur connecté
        }
        setFormations(FormationService.getAll());

      } else if (activeTab === "evenements") {
        const evenementData = {
          title: data.title,
          description: data.description,
          date: data.date,
          lieu: data.lieu || "",
          type: data.type || "",
          participants: data.participants || "",
          contact: data.contact || "",
          status: data.status || "brouillon"
        };

        if (editingItem) {
          EvenementService.update(editingItem.id, evenementData);
        } else {
          EvenementService.create(evenementData, 1); // TODO: Utiliser l'ID de l'utilisateur connecté
        }
        setEvenements(EvenementService.getAll());

      } else if (activeTab === "emplois") {
        const file = selectedFile;
        const emploiData = {
          filiere: data.filiere,
          groupe: data.groupe,
          semaine: data.semaine,
          fileName: file ? file.name : null,
          fileUrl: file ? URL.createObjectURL(file) : null,
          fileSize: file ? file.size : null,
          description: data.description || "",
          status: data.status || "brouillon"
        };

        if (editingItem) {
          EmploiDuTempsService.update(editingItem.id, emploiData);
        } else {
          EmploiDuTempsService.create(emploiData, 1); // TODO: Utiliser l'ID de l'utilisateur connecté
        }
        setEmploisDuTemps(EmploiDuTempsService.getAll());

      } else if (activeTab === "utilisateurs") {
        const utilisateurData = {
          nom: data.nom,
          prenom: data.prenom,
          email: data.email,
          role: data.role || "Administrateur",
          status: data.status || "actif",
          password: data.password || "admin123" // Mot de passe par défaut
        };

        if (editingItem) {
          UserService.update(editingItem.id, utilisateurData);
        } else {
          UserService.create(utilisateurData);
        }
        setUtilisateurs(UserService.getAll());
      }

      setIsAddDialogOpen(false);
      setEditingItem(null);
      setIsFileImport(false);
      setSelectedFile(null);

      toast({
        title: editingItem ? "Modifications enregistrées" :
          activeTab === "emplois" ? "Fichier importé" : "Élément ajouté",
        description: editingItem ?
          "Les changements ont été sauvegardés avec succès." :
          activeTab === "emplois" ?
            "Le fichier d'emploi du temps a été importé avec succès." :
            "Le nouvel élément a été ajouté avec succès."
      });

    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);
      toast({
        title: "Erreur",
        description: "Impossible de sauvegarder les modifications.",
        variant: "destructive"
      });
    }
  };

  const handleCloseDialog = () => {
    setIsAddDialogOpen(false);
    setEditingItem(null);
    setIsFileImport(false);
    setSelectedFile(null);
    setIsDragOver(false);
  };

  const handleFileSelect = (file) => {
    setSelectedFile(file);
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setIsDragOver(false);
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      const file = files[0];
      if (file.type.includes('pdf') || file.type.includes('excel') || file.type.includes('word') ||
          file.name.endsWith('.pdf') || file.name.endsWith('.xlsx') || file.name.endsWith('.xls') ||
          file.name.endsWith('.docx') || file.name.endsWith('.doc')) {
        handleFileSelect(file);
      } else {
        toast({
          title: "Type de fichier non supporté",
          description: "Veuillez sélectionner un fichier PDF, Excel ou Word.",
          variant: "destructive"
        });
      }
    }
  };

  if (!isLoggedIn) {
    return (
      <div className="min-h-screen bg-gray-100 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md mx-auto">
          <Card>
            <CardHeader>
              <CardTitle>Accès Administrateur</CardTitle>
              <CardDescription>
                Veuillez vous authentifier avec vos identifiants.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleLogin} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={loginData.email}
                    onChange={(e) => setLoginData({ ...loginData, email: e.target.value })}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="password">Mot de passe</Label>
                  <Input
                    id="password"
                    type="password"
                    value={loginData.password}
                    onChange={(e) => setLoginData({ ...loginData, password: e.target.value })}
                    required
                  />
                </div>
                <Button type="submit" className="w-full">Se connecter</Button>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Chargement des données...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100">
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <h1 className="text-3xl font-bold text-gray-900">Interface d'administration</h1>
            <Button onClick={handleLogout} variant="outline">Se déconnecter</Button>
          </div>
        </div>
      </div>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-5 gap-4 mb-8">
            <TabsTrigger value="actualites">Actualités</TabsTrigger>
            <TabsTrigger value="formations">Formations</TabsTrigger>
            <TabsTrigger value="evenements">Événements</TabsTrigger>
            <TabsTrigger value="emplois">Emplois du temps</TabsTrigger>
            <TabsTrigger value="utilisateurs">Utilisateurs</TabsTrigger>
          </TabsList>

          <TabsContent value="actualites">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold">Gestion des actualités</h2>
              <Button onClick={handleAdd}>
                <Plus className="mr-2 h-4 w-4" /> Ajouter une actualité
              </Button>
            </div>
            <Card>
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Titre</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Catégorie</TableHead>
                      <TableHead>Statut</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {actualites.map((actualite) => (
                      <TableRow key={actualite.id}>
                        <TableCell className="font-medium">{actualite.title}</TableCell>
                        <TableCell>{new Date(actualite.date).toLocaleDateString('fr-FR')}</TableCell>
                        <TableCell>
                          <Badge variant="outline">{actualite.category}</Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant={actualite.status === "publié" ? "default" : "secondary"}>
                            {actualite.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleEdit(actualite)}
                            >
                              <PenSquare className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="destructive"
                              onClick={() => handleDelete(actualite.id)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="formations">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold">Gestion des formations</h2>
              <Button onClick={handleAdd}>
                <Plus className="mr-2 h-4 w-4" /> Ajouter une formation
              </Button>
            </div>
            <Card>
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Titre</TableHead>
                      <TableHead>Niveau</TableHead>
                      <TableHead>Durée</TableHead>
                      <TableHead>Statut</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {formations.map((formation) => (
                      <TableRow key={formation.id}>
                        <TableCell className="font-medium">{formation.title}</TableCell>
                        <TableCell>
                          <Badge variant="outline">{formation.level}</Badge>
                        </TableCell>
                        <TableCell>{formation.duration}</TableCell>
                        <TableCell>
                          <Badge variant={formation.status === "publié" ? "default" : "secondary"}>
                            {formation.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleEdit(formation)}
                            >
                              <PenSquare className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="destructive"
                              onClick={() => handleDelete(formation.id)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="evenements">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold">Gestion des événements</h2>
              <Button onClick={handleAdd}>
                <Plus className="mr-2 h-4 w-4" /> Ajouter un événement
              </Button>
            </div>
            <Card>
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Titre</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Lieu</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Statut</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {evenements.map((evenement) => (
                      <TableRow key={evenement.id}>
                        <TableCell className="font-medium">{evenement.title}</TableCell>
                        <TableCell>{new Date(evenement.date).toLocaleDateString('fr-FR')}</TableCell>
                        <TableCell>{evenement.lieu}</TableCell>
                        <TableCell>
                          <Badge variant="outline">{evenement.type}</Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant={evenement.status === "publié" ? "default" : "secondary"}>
                            {evenement.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleEdit(evenement)}
                            >
                              <PenSquare className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="destructive"
                              onClick={() => handleDelete(evenement.id)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="emplois">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold">Gestion des emplois du temps</h2>
              <Button onClick={() => handleAdd(true)}>
                <Upload className="mr-2 h-4 w-4" /> Importer un fichier
              </Button>
            </div>
            <Card>
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Filière</TableHead>
                      <TableHead>Groupe</TableHead>
                      <TableHead>Période</TableHead>
                      <TableHead>Fichier</TableHead>
                      <TableHead>Statut</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {emploisDuTemps.map((emploi) => (
                      <TableRow key={emploi.id}>
                        <TableCell className="font-medium">{emploi.filiere}</TableCell>
                        <TableCell>
                          <Badge variant="outline">{emploi.groupe}</Badge>
                        </TableCell>
                        <TableCell>{emploi.semaine}</TableCell>
                        <TableCell>
                          {emploi.fileName ? (
                            <div className="flex items-center">
                              <FileText className="mr-2 h-4 w-4" />
                              <div>
                                <div className="font-medium">{emploi.fileName}</div>
                                <div className="text-sm text-gray-500">
                                  {emploi.fileSize ? `${(emploi.fileSize / 1024).toFixed(1)} KB` : ''}
                                </div>
                              </div>
                            </div>
                          ) : (
                            <span className="text-gray-500">Aucun fichier</span>
                          )}
                        </TableCell>
                        <TableCell>
                          <Badge variant={emploi.status === "publié" ? "default" : "secondary"}>
                            {emploi.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            {emploi.fileUrl && (
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => window.open(emploi.fileUrl, '_blank')}
                              >
                                <Download className="h-4 w-4" />
                              </Button>
                            )}
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleEdit(emploi)}
                            >
                              <PenSquare className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="destructive"
                              onClick={() => handleDelete(emploi.id)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="utilisateurs">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold">Gestion des utilisateurs</h2>
              <Button onClick={handleAdd}>
                <UserPlus className="mr-2 h-4 w-4" /> Ajouter un administrateur
              </Button>
            </div>
            <Card>
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Nom</TableHead>
                      <TableHead>Prénom</TableHead>
                      <TableHead>Email</TableHead>
                      <TableHead>Rôle</TableHead>
                      <TableHead>Statut</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {utilisateurs.map((utilisateur) => (
                      <TableRow key={utilisateur.id}>
                        <TableCell className="font-medium">{utilisateur.nom}</TableCell>
                        <TableCell>{utilisateur.prenom}</TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            <Mail className="mr-2 h-4 w-4" />
                            {utilisateur.email}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            <Shield className="mr-2 h-4 w-4" />
                            <Badge variant="outline">{utilisateur.role}</Badge>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant={utilisateur.status === "actif" ? "default" : "secondary"}>
                            {utilisateur.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleEdit(utilisateur)}
                            >
                              <PenSquare className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="destructive"
                              onClick={() => handleDelete(utilisateur.id)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Dialog pour ajouter/modifier des éléments */}
        <Dialog open={isAddDialogOpen} onOpenChange={handleCloseDialog}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>
                {editingItem ?
                  `Modifier ${activeTab === "actualites" ? "l'actualité" :
                              activeTab === "formations" ? "la formation" :
                              activeTab === "evenements" ? "l'événement" :
                              activeTab === "emplois" ? "l'emploi du temps" :
                              "l'utilisateur"}` :
                  `Ajouter ${activeTab === "actualites" ? "une actualité" :
                            activeTab === "formations" ? "une formation" :
                            activeTab === "evenements" ? "un événement" :
                            activeTab === "emplois" ? "un emploi du temps" :
                            "un utilisateur"}`
                }
              </DialogTitle>
              <DialogDescription>
                {isFileImport ?
                  "Importez un fichier d'emploi du temps (PDF, Excel ou Word)" :
                  "Remplissez les informations ci-dessous"
                }
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleSave}>
              <div className="grid gap-4 py-4">
                {activeTab === "actualites" && (
                  <>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="title" className="text-right">Titre</Label>
                      <Input
                        id="title"
                        name="title"
                        defaultValue={editingItem?.title || ""}
                        className="col-span-3"
                        required
                      />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="content" className="text-right">Contenu</Label>
                      <Textarea
                        id="content"
                        name="content"
                        defaultValue={editingItem?.content || ""}
                        className="col-span-3"
                        rows={4}
                        required
                      />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="category" className="text-right">Catégorie</Label>
                      <Select name="category" defaultValue={editingItem?.category || "formation"}>
                        <SelectTrigger className="col-span-3">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="formation">Formation</SelectItem>
                          <SelectItem value="evenement">Événement</SelectItem>
                          <SelectItem value="general">Général</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="author" className="text-right">Auteur</Label>
                      <Input
                        id="author"
                        name="author"
                        defaultValue={editingItem?.author || "Admin"}
                        className="col-span-3"
                      />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="image" className="text-right">Image URL</Label>
                      <Input
                        id="image"
                        name="image"
                        type="url"
                        defaultValue={editingItem?.image_url || ""}
                        className="col-span-3"
                        placeholder="https://..."
                      />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="status" className="text-right">Statut</Label>
                      <Select name="status" defaultValue={editingItem?.status || "brouillon"}>
                        <SelectTrigger className="col-span-3">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="brouillon">Brouillon</SelectItem>
                          <SelectItem value="publié">Publié</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </>
                )}

                {activeTab === "formations" && (
                  <>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="title" className="text-right">Titre</Label>
                      <Input
                        id="title"
                        name="title"
                        defaultValue={editingItem?.title || ""}
                        className="col-span-3"
                        required
                      />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="description" className="text-right">Description</Label>
                      <Textarea
                        id="description"
                        name="description"
                        defaultValue={editingItem?.description || ""}
                        className="col-span-3"
                        rows={3}
                        required
                      />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="level" className="text-right">Niveau</Label>
                      <Input
                        id="level"
                        name="level"
                        defaultValue={editingItem?.level || ""}
                        className="col-span-3"
                        placeholder="Ex: Débutant, Intermédiaire, Avancé"
                      />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="duration" className="text-right">Durée</Label>
                      <Input
                        id="duration"
                        name="duration"
                        defaultValue={editingItem?.duration || ""}
                        className="col-span-3"
                        placeholder="Ex: 6 mois, 1 an"
                      />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="prerequisites" className="text-right">Prérequis</Label>
                      <Textarea
                        id="prerequisites"
                        name="prerequisites"
                        defaultValue={editingItem?.prerequisites || ""}
                        className="col-span-3"
                        rows={2}
                      />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="status" className="text-right">Statut</Label>
                      <Select name="status" defaultValue={editingItem?.status || "brouillon"}>
                        <SelectTrigger className="col-span-3">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="brouillon">Brouillon</SelectItem>
                          <SelectItem value="publié">Publié</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </>
                )}

                {activeTab === "evenements" && (
                  <>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="title" className="text-right">Titre</Label>
                      <Input
                        id="title"
                        name="title"
                        defaultValue={editingItem?.title || ""}
                        className="col-span-3"
                        required
                      />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="description" className="text-right">Description</Label>
                      <Textarea
                        id="description"
                        name="description"
                        defaultValue={editingItem?.description || ""}
                        className="col-span-3"
                        rows={3}
                        required
                      />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="date" className="text-right">Date</Label>
                      <Input
                        id="date"
                        name="date"
                        type="date"
                        defaultValue={editingItem?.date || ""}
                        className="col-span-3"
                        required
                      />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="lieu" className="text-right">Lieu</Label>
                      <Input
                        id="lieu"
                        name="lieu"
                        defaultValue={editingItem?.lieu || ""}
                        className="col-span-3"
                      />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="type" className="text-right">Type</Label>
                      <Input
                        id="type"
                        name="type"
                        defaultValue={editingItem?.type || ""}
                        className="col-span-3"
                        placeholder="Ex: Conférence, Atelier, Séminaire"
                      />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="contact" className="text-right">Contact</Label>
                      <Input
                        id="contact"
                        name="contact"
                        defaultValue={editingItem?.contact || ""}
                        className="col-span-3"
                        placeholder="Email ou téléphone"
                      />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="status" className="text-right">Statut</Label>
                      <Select name="status" defaultValue={editingItem?.status || "brouillon"}>
                        <SelectTrigger className="col-span-3">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="brouillon">Brouillon</SelectItem>
                          <SelectItem value="publié">Publié</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </>
                )}

                {activeTab === "emplois" && (
                  <>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="filiere" className="text-right">Filière</Label>
                      <Select name="filiere" defaultValue={editingItem?.filiere || ""}>
                        <SelectTrigger className="col-span-3">
                          <SelectValue placeholder="Sélectionner une filière" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="BTS ACSE">BTS ACSE (Analyse, Conduite et Stratégie de l'Entreprise Agricole)</SelectItem>
                          <SelectItem value="BTS PA">BTS PA (Productions Animales)</SelectItem>
                          <SelectItem value="BTS PV">BTS PV (Productions Végétales)</SelectItem>
                          <SelectItem value="CAP Agriculture">CAP Métiers de l'Agriculture</SelectItem>
                          <SelectItem value="CAP Elevage">CAP Métiers de l'Élevage</SelectItem>
                          <SelectItem value="Bac Pro CGEA">Bac Pro CGEA (Conduite et Gestion de l'Entreprise Agricole)</SelectItem>
                          <SelectItem value="Bac Pro CGEH">Bac Pro CGEH (Conduite et Gestion de l'Entreprise Hippique)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="groupe" className="text-right">Groupe</Label>
                      <Select name="groupe" defaultValue={editingItem?.groupe || ""}>
                        <SelectTrigger className="col-span-3">
                          <SelectValue placeholder="Sélectionner un groupe" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="1ère année">1ère année</SelectItem>
                          <SelectItem value="2ème année">2ème année</SelectItem>
                          <SelectItem value="Groupe A">Groupe A</SelectItem>
                          <SelectItem value="Groupe B">Groupe B</SelectItem>
                          <SelectItem value="Groupe C">Groupe C</SelectItem>
                          <SelectItem value="Apprentissage">Apprentissage</SelectItem>
                          <SelectItem value="Formation Continue">Formation Continue</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="semaine" className="text-right">Période</Label>
                      <Input
                        id="semaine"
                        name="semaine"
                        defaultValue={editingItem?.semaine || ""}
                        className="col-span-3"
                        placeholder="Ex: Semaine du 15-21 Janvier 2024"
                        required
                      />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="description" className="text-right">Description</Label>
                      <Textarea
                        id="description"
                        name="description"
                        defaultValue={editingItem?.description || ""}
                        className="col-span-3"
                        rows={2}
                        placeholder="Description optionnelle"
                      />
                    </div>
                    {isFileImport && (
                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label className="text-right">Fichier</Label>
                        <div className="col-span-3">
                          <div
                            className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
                              isDragOver ? 'border-blue-400 bg-blue-50' : 'border-gray-300'
                            }`}
                            onDragOver={handleDragOver}
                            onDragLeave={handleDragLeave}
                            onDrop={handleDrop}
                          >
                            {selectedFile ? (
                              <div className="flex items-center justify-center">
                                <FileText className="mr-2 h-8 w-8 text-blue-500" />
                                <div>
                                  <p className="font-medium">{selectedFile.name}</p>
                                  <p className="text-sm text-gray-500">
                                    {(selectedFile.size / 1024).toFixed(1)} KB
                                  </p>
                                </div>
                              </div>
                            ) : (
                              <div>
                                <Upload className="mx-auto h-12 w-12 text-gray-400" />
                                <p className="mt-2 text-sm text-gray-600">
                                  Glissez-déposez un fichier ici ou{' '}
                                  <label className="text-blue-600 hover:text-blue-500 cursor-pointer">
                                    parcourez
                                    <input
                                      type="file"
                                      className="hidden"
                                      accept=".pdf,.xlsx,.xls,.docx,.doc"
                                      onChange={(e) => {
                                        const file = e.target.files?.[0];
                                        if (file) handleFileSelect(file);
                                      }}
                                    />
                                  </label>
                                </p>
                                <p className="text-xs text-gray-500 mt-1">
                                  PDF, Excel ou Word uniquement
                                </p>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    )}
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="status" className="text-right">Statut</Label>
                      <Select name="status" defaultValue={editingItem?.status || "brouillon"}>
                        <SelectTrigger className="col-span-3">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="brouillon">Brouillon</SelectItem>
                          <SelectItem value="publié">Publié</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </>
                )}

                {activeTab === "utilisateurs" && (
                  <>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="nom" className="text-right">Nom</Label>
                      <Input
                        id="nom"
                        name="nom"
                        defaultValue={editingItem?.nom || ""}
                        className="col-span-3"
                        required
                      />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="prenom" className="text-right">Prénom</Label>
                      <Input
                        id="prenom"
                        name="prenom"
                        defaultValue={editingItem?.prenom || ""}
                        className="col-span-3"
                        required
                      />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="email" className="text-right">Email</Label>
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        defaultValue={editingItem?.email || ""}
                        className="col-span-3"
                        required
                      />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="role" className="text-right">Rôle</Label>
                      <Select name="role" defaultValue={editingItem?.role || "Administrateur"}>
                        <SelectTrigger className="col-span-3">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Administrateur">Administrateur</SelectItem>
                          <SelectItem value="Super Admin">Super Admin</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    {!editingItem && (
                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="password" className="text-right">Mot de passe</Label>
                        <Input
                          id="password"
                          name="password"
                          type="password"
                          defaultValue="admin123"
                          className="col-span-3"
                          required
                        />
                      </div>
                    )}
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="status" className="text-right">Statut</Label>
                      <Select name="status" defaultValue={editingItem?.status || "actif"}>
                        <SelectTrigger className="col-span-3">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="actif">Actif</SelectItem>
                          <SelectItem value="inactif">Inactif</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </>
                )}
              </div>
              <DialogFooter>
                <Button type="button" variant="outline" onClick={handleCloseDialog}>
                  Annuler
                </Button>
                <Button type="submit">
                  {editingItem ? "Sauvegarder" : "Ajouter"}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </main>
    </div>
  );
};

export default Admin;
