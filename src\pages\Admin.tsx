
import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { Plus, PenSquare, Trash2, Calendar, Clock, Users, BookO<PERSON>, Image, FileText, Upload, Download } from "lucide-react";

const Admin = () => {
  const { toast } = useToast();
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [loginData, setLoginData] = useState({
    email: "",
    password: ""
  });
  const [activeTab, setActiveTab] = useState("actualites");
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [editingItem, setEditingItem] = useState(null);
  const [isFileImport, setIsFileImport] = useState(false);
  const [selectedFile, setSelectedFile] = useState(null);
  const [isDragOver, setIsDragOver] = useState(false);

  // Données d'exemple pour les actualités
  const [actualites, setActualites] = useState([
    {
      id: 1,
      title: "Nouvelle formation en Agriculture Durable",
      content: "Nous sommes ravis d'annoncer le lancement de notre nouvelle formation...",
      date: "2024-03-20",
      category: "formation",
      status: "publié",
      image: "/images/formation-agriculture.jpg",
      author: "Direction Pédagogique"
    },
    {
      id: 2,
      title: "Journée Portes Ouvertes 2024",
      content: "Venez découvrir nos formations et nos installations lors de notre journée portes ouvertes...",
      date: "2024-04-15",
      category: "evenement",
      status: "brouillon",
      image: "/images/portes-ouvertes.jpg",
      author: "Service Communication"
    }
  ]);

  // Données d'exemple pour les formations
  const [formations, setFormations] = useState([
    {
      id: 1,
      title: "BTS Analyse, Conduite et Stratégie de l'Entreprise Agricole",
      description: "Formation complète en gestion d'entreprise agricole",
      duration: "2 ans",
      level: "Bac+2",
      status: "publié",
      prerequisites: "Baccalauréat",
      objectives: "Former des responsables d'exploitation agricole",
      program: "Économie, Gestion, Techniques agricoles, Comptabilité",
      career: "Chef d'exploitation, Conseiller agricole"
    },
    {
      id: 2,
      title: "CAP Métiers de l'Agriculture",
      description: "Formation pratique aux métiers de l'agriculture",
      duration: "2 ans",
      level: "CAP",
      status: "publié",
      prerequisites: "3ème",
      objectives: "Acquérir les compétences de base en agriculture",
      program: "Productions végétales, Productions animales, Agroéquipement",
      career: "Ouvrier agricole, Aide-éleveur"
    }
  ]);

  // Données d'exemple pour les emplois du temps
  const [emploisDuTemps, setEmploisDuTemps] = useState([
    {
      id: 1,
      classe: "BTS ACSE 1ère année",
      semaine: "Semaine du 18-22 Mars 2024",
      status: "publié",
      fileName: "emploi_bts_acse_1.pdf",
      fileUrl: "/uploads/emploi_bts_acse_1.pdf",
      uploadDate: "2024-03-15",
      horaires: [
        { jour: "Lundi", heures: "8h-10h", matiere: "Économie", professeur: "M. Dubois", salle: "A101" },
        { jour: "Lundi", heures: "10h-12h", matiere: "Gestion", professeur: "Mme Martin", salle: "A102" },
        { jour: "Mardi", heures: "8h-10h", matiere: "Techniques agricoles", professeur: "M. Bernard", salle: "Atelier" }
      ]
    }
  ]);

  // Données d'exemple pour les événements
  const [evenements, setEvenements] = useState([
    {
      id: 1,
      title: "Salon de l'Agriculture",
      description: "Participation au salon national de l'agriculture",
      date: "2024-05-15",
      lieu: "Paris Expo",
      type: "Salon",
      status: "publié",
      participants: "Étudiants BTS",
      contact: "<EMAIL>"
    }
  ]);

  const handleLogin = (e) => {
    e.preventDefault();
    // Vérification des identifiants (à implémenter avec le backend)
    setIsLoggedIn(true);
    toast({
      title: "Connexion réussie",
      description: "Bienvenue dans l'interface d'administration."
    });
  };

  const handleLogout = () => {
    setIsLoggedIn(false);
    setLoginData({ email: "", password: "" });
  };

  const handleAdd = (isImport = false) => {
    setEditingItem(null);
    setIsFileImport(isImport);
    setIsAddDialogOpen(true);
  };

  const handleEdit = (item) => {
    setEditingItem(item);
    setIsAddDialogOpen(true);
  };

  const handleDelete = (id) => {
    // Logique de suppression selon le type de contenu
    if (activeTab === "actualites") {
      setActualites(actualites.filter(item => item.id !== id));
    } else if (activeTab === "formations") {
      setFormations(formations.filter(item => item.id !== id));
    } else if (activeTab === "evenements") {
      setEvenements(evenements.filter(item => item.id !== id));
    } else if (activeTab === "emplois") {
      setEmploisDuTemps(emploisDuTemps.filter(item => item.id !== id));
    }

    toast({
      title: "Élément supprimé",
      description: "L'élément a été supprimé avec succès."
    });
  };

  const handleSave = (e) => {
    e.preventDefault();
    const formData = new FormData(e.target);
    const data = Object.fromEntries(formData.entries());

    // Générer un nouvel ID si c'est un ajout
    const newId = editingItem ? editingItem.id : Date.now();

    if (activeTab === "actualites") {
      const newActualite = {
        id: newId,
        title: data.title,
        content: data.content,
        date: new Date().toISOString().split('T')[0],
        category: data.category || "general",
        status: data.status || "brouillon",
        image: data.image || "",
        author: data.author || "Admin"
      };

      if (editingItem) {
        setActualites(actualites.map(item =>
          item.id === editingItem.id ? newActualite : item
        ));
      } else {
        setActualites([...actualites, newActualite]);
      }
    } else if (activeTab === "formations") {
      const newFormation = {
        id: newId,
        title: data.title,
        description: data.description,
        duration: data.duration || "",
        level: data.level || "",
        status: data.status || "brouillon",
        prerequisites: data.prerequisites || "",
        objectives: data.objectives || "",
        program: data.program || "",
        career: data.career || ""
      };

      if (editingItem) {
        setFormations(formations.map(item =>
          item.id === editingItem.id ? newFormation : item
        ));
      } else {
        setFormations([...formations, newFormation]);
      }
    } else if (activeTab === "evenements") {
      const newEvenement = {
        id: newId,
        title: data.title,
        description: data.description,
        date: data.date,
        lieu: data.lieu || "",
        type: data.type || "",
        status: data.status || "brouillon",
        participants: data.participants || "",
        contact: data.contact || ""
      };

      if (editingItem) {
        setEvenements(evenements.map(item =>
          item.id === editingItem.id ? newEvenement : item
        ));
      } else {
        setEvenements([...evenements, newEvenement]);
      }
    } else if (activeTab === "emplois") {
      const file = selectedFile;

      const newEmploi = {
        id: newId,
        classe: data.classe,
        semaine: data.semaine,
        status: data.status || "brouillon",
        fileName: file ? file.name : null,
        fileUrl: file ? URL.createObjectURL(file) : null,
        uploadDate: file ? new Date().toISOString().split('T')[0] : null,
        description: data.description || "",
        horaires: [] // Pour la création manuelle
      };

      if (editingItem) {
        setEmploisDuTemps(emploisDuTemps.map(item =>
          item.id === editingItem.id ? newEmploi : item
        ));
      } else {
        setEmploisDuTemps([...emploisDuTemps, newEmploi]);
      }
    }

    setIsAddDialogOpen(false);
    setEditingItem(null);
    setIsFileImport(false);
    setSelectedFile(null);
    toast({
      title: editingItem ? "Modifications enregistrées" :
        activeTab === "emplois" ? "Fichier importé" : "Élément ajouté",
      description: editingItem ?
        "Les changements ont été sauvegardés avec succès." :
        activeTab === "emplois" ?
          "Le fichier d'emploi du temps a été importé avec succès." :
          "Le nouvel élément a été ajouté avec succès."
    });
  };

  const handleCloseDialog = () => {
    setIsAddDialogOpen(false);
    setEditingItem(null);
    setIsFileImport(false);
    setSelectedFile(null);
    setIsDragOver(false);
  };

  const handleFileSelect = (file) => {
    setSelectedFile(file);
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setIsDragOver(false);
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      const file = files[0];
      if (file.type.includes('pdf') || file.type.includes('excel') || file.type.includes('word') ||
          file.name.endsWith('.pdf') || file.name.endsWith('.xlsx') || file.name.endsWith('.xls') ||
          file.name.endsWith('.docx') || file.name.endsWith('.doc')) {
        handleFileSelect(file);
      } else {
        toast({
          title: "Type de fichier non supporté",
          description: "Veuillez sélectionner un fichier PDF, Excel ou Word.",
          variant: "destructive"
        });
      }
    }
  };

  if (!isLoggedIn) {
    return (
      <div className="min-h-screen bg-gray-100 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md mx-auto">
          <Card>
            <CardHeader>
              <CardTitle>Accès Administrateur</CardTitle>
              <CardDescription>
                Veuillez vous authentifier avec vos identifiants.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleLogin} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={loginData.email}
                    onChange={(e) => setLoginData({ ...loginData, email: e.target.value })}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="password">Mot de passe</Label>
                  <Input
                    id="password"
                    type="password"
                    value={loginData.password}
                    onChange={(e) => setLoginData({ ...loginData, password: e.target.value })}
                    required
                  />
                </div>
                <Button type="submit" className="w-full">Se connecter</Button>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100">
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <h1 className="text-3xl font-bold text-gray-900">Interface d'administration</h1>
            <Button onClick={handleLogout} variant="outline">Se déconnecter</Button>
          </div>
        </div>
      </div>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-4 gap-4 mb-8">
            <TabsTrigger value="actualites">Actualités</TabsTrigger>
            <TabsTrigger value="formations">Formations</TabsTrigger>
            <TabsTrigger value="evenements">Événements</TabsTrigger>
            <TabsTrigger value="emplois">Emplois du temps</TabsTrigger>
          </TabsList>

          <TabsContent value="actualites">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold">Gestion des actualités</h2>
              <Button onClick={handleAdd}>
                <Plus className="mr-2 h-4 w-4" /> Ajouter une actualité
              </Button>
            </div>
            <Card>
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Titre</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Catégorie</TableHead>
                      <TableHead>Statut</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {actualites.map((actualite) => (
                      <TableRow key={actualite.id}>
                        <TableCell className="font-medium">{actualite.title}</TableCell>
                        <TableCell>{new Date(actualite.date).toLocaleDateString('fr-FR')}</TableCell>
                        <TableCell>
                          <Badge variant="outline">{actualite.category}</Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant={actualite.status === "publié" ? "default" : "secondary"}>
                            {actualite.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleEdit(actualite)}
                            >
                              <PenSquare className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="destructive"
                              onClick={() => handleDelete(actualite.id)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="formations">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold">Gestion des formations</h2>
              <Button onClick={handleAdd}>
                <Plus className="mr-2 h-4 w-4" /> Ajouter une formation
              </Button>
            </div>
            <Card>
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Titre</TableHead>
                      <TableHead>Niveau</TableHead>
                      <TableHead>Durée</TableHead>
                      <TableHead>Statut</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {formations.map((formation) => (
                      <TableRow key={formation.id}>
                        <TableCell className="font-medium">{formation.title}</TableCell>
                        <TableCell>
                          <Badge variant="outline">{formation.level}</Badge>
                        </TableCell>
                        <TableCell>{formation.duration}</TableCell>
                        <TableCell>
                          <Badge variant={formation.status === "publié" ? "default" : "secondary"}>
                            {formation.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleEdit(formation)}
                            >
                              <PenSquare className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="destructive"
                              onClick={() => handleDelete(formation.id)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="evenements">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold">Gestion des événements</h2>
              <Button onClick={handleAdd}>
                <Plus className="mr-2 h-4 w-4" /> Ajouter un événement
              </Button>
            </div>
            <Card>
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Titre</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Lieu</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Statut</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {evenements.map((evenement) => (
                      <TableRow key={evenement.id}>
                        <TableCell className="font-medium">{evenement.title}</TableCell>
                        <TableCell>{new Date(evenement.date).toLocaleDateString('fr-FR')}</TableCell>
                        <TableCell>{evenement.lieu}</TableCell>
                        <TableCell>
                          <Badge variant="outline">{evenement.type}</Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant={evenement.status === "publié" ? "default" : "secondary"}>
                            {evenement.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleEdit(evenement)}
                            >
                              <PenSquare className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="destructive"
                              onClick={() => handleDelete(evenement.id)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="emplois">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold">Gestion des emplois du temps</h2>
              <Button onClick={() => handleAdd(true)}>
                <Upload className="mr-2 h-4 w-4" /> Importer un emploi du temps
              </Button>
            </div>
            <Card>
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Classe</TableHead>
                      <TableHead>Semaine</TableHead>
                      <TableHead>Fichier</TableHead>
                      <TableHead>Date d'import</TableHead>
                      <TableHead>Statut</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {emploisDuTemps.map((emploi) => (
                      <TableRow key={emploi.id}>
                        <TableCell className="font-medium">{emploi.classe}</TableCell>
                        <TableCell>{emploi.semaine}</TableCell>
                        <TableCell>
                          {emploi.fileName ? (
                            <div className="flex items-center space-x-2">
                              <FileText className="h-4 w-4 text-blue-500" />
                              <span className="text-sm">{emploi.fileName}</span>
                            </div>
                          ) : (
                            <span className="text-gray-400 text-sm">Création manuelle</span>
                          )}
                        </TableCell>
                        <TableCell>
                          {emploi.uploadDate ?
                            new Date(emploi.uploadDate).toLocaleDateString('fr-FR') :
                            '-'
                          }
                        </TableCell>
                        <TableCell>
                          <Badge variant={emploi.status === "publié" ? "default" : "secondary"}>
                            {emploi.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            {emploi.fileUrl && (
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => window.open(emploi.fileUrl, '_blank')}
                                title="Télécharger le fichier"
                              >
                                <Download className="h-4 w-4" />
                              </Button>
                            )}
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleEdit(emploi)}
                            >
                              <PenSquare className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="destructive"
                              onClick={() => handleDelete(emploi.id)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <Dialog open={isAddDialogOpen} onOpenChange={handleCloseDialog}>
          <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>
                {editingItem ? "Modifier" :
                  activeTab === "emplois" ? "Importer" : "Ajouter"
                } {
                  activeTab === "actualites" ? "une actualité" :
                  activeTab === "formations" ? "une formation" :
                  activeTab === "evenements" ? "un événement" :
                  "un emploi du temps"
                }
              </DialogTitle>
            </DialogHeader>
            <form onSubmit={handleSave} className="space-y-6">
              <div className="space-y-4">
                {/* Formulaire pour les actualités */}
                {activeTab === "actualites" && (
                  <>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="title">Titre *</Label>
                        <Input
                          id="title"
                          name="title"
                          defaultValue={editingItem?.title}
                          placeholder="Titre de l'actualité"
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="author">Auteur</Label>
                        <Input
                          id="author"
                          name="author"
                          defaultValue={editingItem?.author}
                          placeholder="Nom de l'auteur"
                        />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="content">Contenu *</Label>
                      <Textarea
                        id="content"
                        name="content"
                        defaultValue={editingItem?.content}
                        placeholder="Contenu de l'actualité..."
                        className="min-h-[120px]"
                        required
                      />
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="category">Catégorie</Label>
                        <Select name="category" defaultValue={editingItem?.category}>
                          <SelectTrigger>
                            <SelectValue placeholder="Sélectionner une catégorie" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="formation">Formation</SelectItem>
                            <SelectItem value="infrastructure">Infrastructure</SelectItem>
                            <SelectItem value="partenariat">Partenariat</SelectItem>
                            <SelectItem value="pedagogie">Pédagogie</SelectItem>
                            <SelectItem value="vie-etudiante">Vie étudiante</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="status">Statut</Label>
                        <Select name="status" defaultValue={editingItem?.status || "brouillon"}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="brouillon">Brouillon</SelectItem>
                            <SelectItem value="publié">Publié</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="image">Image (URL)</Label>
                      <Input
                        id="image"
                        name="image"
                        defaultValue={editingItem?.image}
                        placeholder="URL de l'image"
                        type="url"
                      />
                    </div>
                  </>
                )}

                {/* Formulaire pour les formations */}
                {activeTab === "formations" && (
                  <>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="title">Titre de la formation *</Label>
                        <Input
                          id="title"
                          name="title"
                          defaultValue={editingItem?.title}
                          placeholder="Nom de la formation"
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="level">Niveau</Label>
                        <Select name="level" defaultValue={editingItem?.level}>
                          <SelectTrigger>
                            <SelectValue placeholder="Niveau de formation" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="CAP">CAP</SelectItem>
                            <SelectItem value="Bac Pro">Bac Pro</SelectItem>
                            <SelectItem value="BTS">BTS</SelectItem>
                            <SelectItem value="Licence">Licence</SelectItem>
                            <SelectItem value="Formation continue">Formation continue</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="description">Description *</Label>
                      <Textarea
                        id="description"
                        name="description"
                        defaultValue={editingItem?.description}
                        placeholder="Description de la formation"
                        required
                      />
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="duration">Durée</Label>
                        <Input
                          id="duration"
                          name="duration"
                          defaultValue={editingItem?.duration}
                          placeholder="ex: 2 ans, 6 mois"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="prerequisites">Prérequis</Label>
                        <Input
                          id="prerequisites"
                          name="prerequisites"
                          defaultValue={editingItem?.prerequisites}
                          placeholder="ex: Baccalauréat, 3ème"
                        />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="objectives">Objectifs</Label>
                      <Textarea
                        id="objectives"
                        name="objectives"
                        defaultValue={editingItem?.objectives}
                        placeholder="Objectifs de la formation"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="program">Programme</Label>
                      <Textarea
                        id="program"
                        name="program"
                        defaultValue={editingItem?.program}
                        placeholder="Programme détaillé"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="career">Débouchés</Label>
                      <Textarea
                        id="career"
                        name="career"
                        defaultValue={editingItem?.career}
                        placeholder="Métiers et débouchés"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="status">Statut</Label>
                      <Select name="status" defaultValue={editingItem?.status || "brouillon"}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="brouillon">Brouillon</SelectItem>
                          <SelectItem value="publié">Publié</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </>
                )}

                {/* Formulaire pour les événements */}
                {activeTab === "evenements" && (
                  <>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="title">Titre de l'événement *</Label>
                        <Input
                          id="title"
                          name="title"
                          defaultValue={editingItem?.title}
                          placeholder="Nom de l'événement"
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="date">Date *</Label>
                        <Input
                          id="date"
                          name="date"
                          type="date"
                          defaultValue={editingItem?.date}
                          required
                        />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="description">Description *</Label>
                      <Textarea
                        id="description"
                        name="description"
                        defaultValue={editingItem?.description}
                        placeholder="Description de l'événement"
                        required
                      />
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="lieu">Lieu</Label>
                        <Input
                          id="lieu"
                          name="lieu"
                          defaultValue={editingItem?.lieu}
                          placeholder="Lieu de l'événement"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="type">Type d'événement</Label>
                        <Select name="type" defaultValue={editingItem?.type}>
                          <SelectTrigger>
                            <SelectValue placeholder="Type d'événement" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="Salon">Salon</SelectItem>
                            <SelectItem value="Conférence">Conférence</SelectItem>
                            <SelectItem value="Portes ouvertes">Portes ouvertes</SelectItem>
                            <SelectItem value="Sortie pédagogique">Sortie pédagogique</SelectItem>
                            <SelectItem value="Concours">Concours</SelectItem>
                            <SelectItem value="Cérémonie">Cérémonie</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="participants">Participants</Label>
                        <Input
                          id="participants"
                          name="participants"
                          defaultValue={editingItem?.participants}
                          placeholder="Public concerné"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="contact">Contact</Label>
                        <Input
                          id="contact"
                          name="contact"
                          defaultValue={editingItem?.contact}
                          placeholder="Email ou téléphone de contact"
                        />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="status">Statut</Label>
                      <Select name="status" defaultValue={editingItem?.status || "brouillon"}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="brouillon">Brouillon</SelectItem>
                          <SelectItem value="publié">Publié</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </>
                )}

                {/* Formulaire pour les emplois du temps */}
                {activeTab === "emplois" && (
                  <>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="classe">Classe *</Label>
                        <Select name="classe" defaultValue={editingItem?.classe}>
                          <SelectTrigger>
                            <SelectValue placeholder="Sélectionner une classe" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="BTS ACSE 1ère année">BTS ACSE 1ère année</SelectItem>
                            <SelectItem value="BTS ACSE 2ème année">BTS ACSE 2ème année</SelectItem>
                            <SelectItem value="CAP Agriculture 1ère année">CAP Agriculture 1ère année</SelectItem>
                            <SelectItem value="CAP Agriculture 2ème année">CAP Agriculture 2ème année</SelectItem>
                            <SelectItem value="Bac Pro CGEA 1ère année">Bac Pro CGEA 1ère année</SelectItem>
                            <SelectItem value="Bac Pro CGEA 2ème année">Bac Pro CGEA 2ème année</SelectItem>
                            <SelectItem value="Bac Pro CGEA 3ème année">Bac Pro CGEA 3ème année</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="semaine">Semaine *</Label>
                        <Input
                          id="semaine"
                          name="semaine"
                          defaultValue={editingItem?.semaine}
                          placeholder="ex: Semaine du 18-22 Mars 2024"
                          required
                        />
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="file">Fichier d'emploi du temps *</Label>
                        <div
                          className={`border-2 border-dashed rounded-lg p-6 transition-colors ${
                            isDragOver
                              ? 'border-blue-400 bg-blue-50'
                              : selectedFile
                                ? 'border-green-400 bg-green-50'
                                : 'border-gray-300 hover:border-gray-400'
                          }`}
                          onDragOver={handleDragOver}
                          onDragLeave={handleDragLeave}
                          onDrop={handleDrop}
                        >
                          <div className="text-center">
                            {selectedFile ? (
                              <div className="space-y-2">
                                <FileText className="mx-auto h-12 w-12 text-green-500" />
                                <div>
                                  <p className="text-sm font-medium text-green-700">
                                    {selectedFile.name}
                                  </p>
                                  <p className="text-xs text-green-600">
                                    {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                                  </p>
                                </div>
                                <Button
                                  type="button"
                                  variant="outline"
                                  size="sm"
                                  onClick={() => setSelectedFile(null)}
                                >
                                  Changer de fichier
                                </Button>
                              </div>
                            ) : (
                              <>
                                <Upload className={`mx-auto h-12 w-12 ${isDragOver ? 'text-blue-500' : 'text-gray-400'}`} />
                                <div className="mt-4">
                                  <Label htmlFor="file-upload" className="cursor-pointer">
                                    <span className="mt-2 block text-sm font-medium text-gray-900">
                                      {isDragOver ? 'Déposez le fichier ici' : 'Cliquez pour sélectionner ou glissez-déposez'}
                                    </span>
                                    <span className="mt-1 block text-xs text-gray-500">
                                      PDF, Excel, Word (max. 10MB)
                                    </span>
                                  </Label>
                                  <Input
                                    id="file-upload"
                                    name="file"
                                    type="file"
                                    className="sr-only"
                                    accept=".pdf,.xlsx,.xls,.docx,.doc"
                                    required
                                    onChange={(e) => {
                                      const file = e.target.files[0];
                                      if (file) handleFileSelect(file);
                                    }}
                                  />
                                </div>
                              </>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="description">Description du fichier</Label>
                        <Textarea
                          id="description"
                          name="description"
                          placeholder="Description optionnelle du contenu du fichier..."
                          rows={3}
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="status">Statut</Label>
                      <Select name="status" defaultValue={editingItem?.status || "brouillon"}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="brouillon">Brouillon</SelectItem>
                          <SelectItem value="publié">Publié</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </>
                )}
              </div>
              <DialogFooter>
                <Button type="button" variant="outline" onClick={handleCloseDialog}>
                  Annuler
                </Button>
                <Button type="submit">
                  {editingItem ? "Enregistrer les modifications" :
                    activeTab === "emplois" ? "Importer le fichier" : "Ajouter"
                  }
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </main>
    </div>
  );
};

export default Admin;
