/**
 * Module de connexion à la base de données SQLite
 * Fournit une interface simple pour interagir avec la base de données
 */

const Database = require('better-sqlite3');
const path = require('path');

const DB_PATH = path.join(__dirname, 'cmc_database.sqlite');

class DatabaseConnection {
  constructor() {
    this.db = null;
  }

  // Ouvrir la connexion à la base de données
  connect() {
    try {
      this.db = new Database(DB_PATH);
      console.log('✅ Connexion à la base de données établie');
      return this.db;
    } catch (error) {
      console.error('❌ Erreur de connexion à la base de données:', error.message);
      throw error;
    }
  }

  // Fermer la connexion
  close() {
    if (this.db) {
      this.db.close();
      console.log('✅ Connexion à la base de données fermée');
    }
  }

  // Obtenir la connexion (la créer si nécessaire)
  getConnection() {
    if (!this.db) {
      this.connect();
    }
    return this.db;
  }

  // Exécuter une requête SELECT
  select(query, params = []) {
    const db = this.getConnection();
    try {
      const stmt = db.prepare(query);
      return stmt.all(params);
    } catch (error) {
      console.error('❌ Erreur SELECT:', error.message);
      throw error;
    }
  }

  // Exécuter une requête SELECT pour un seul résultat
  selectOne(query, params = []) {
    const db = this.getConnection();
    try {
      const stmt = db.prepare(query);
      return stmt.get(params);
    } catch (error) {
      console.error('❌ Erreur SELECT ONE:', error.message);
      throw error;
    }
  }

  // Exécuter une requête INSERT
  insert(query, params = []) {
    const db = this.getConnection();
    try {
      const stmt = db.prepare(query);
      const result = stmt.run(params);
      return result.lastInsertRowid;
    } catch (error) {
      console.error('❌ Erreur INSERT:', error.message);
      throw error;
    }
  }

  // Exécuter une requête UPDATE
  update(query, params = []) {
    const db = this.getConnection();
    try {
      const stmt = db.prepare(query);
      const result = stmt.run(params);
      return result.changes;
    } catch (error) {
      console.error('❌ Erreur UPDATE:', error.message);
      throw error;
    }
  }

  // Exécuter une requête DELETE
  delete(query, params = []) {
    const db = this.getConnection();
    try {
      const stmt = db.prepare(query);
      const result = stmt.run(params);
      return result.changes;
    } catch (error) {
      console.error('❌ Erreur DELETE:', error.message);
      throw error;
    }
  }

  // Exécuter une transaction
  transaction(callback) {
    const db = this.getConnection();
    const transaction = db.transaction(callback);
    return transaction;
  }
}

// Exporter une instance singleton
const dbConnection = new DatabaseConnection();

module.exports = dbConnection;
