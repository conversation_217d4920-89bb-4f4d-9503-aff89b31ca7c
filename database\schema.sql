-- Base de données pour le site CMC (Centre de Formation Agricole)
-- Création des tables principales

-- Table des utilisateurs administrateurs
CREATE TABLE IF NOT EXISTS utilisateurs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    nom VARCHAR(100) NOT NULL,
    prenom VARCHAR(100) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role ENUM('Administrateur principal', 'Administrateur', 'Administrateur lecture seule') DEFAULT 'Administrateur',
    status ENUM('actif', 'inactif', 'suspendu') DEFAULT 'actif',
    date_creation DATETIME DEFAULT CURRENT_TIMESTAMP,
    dernier_acces DATETIME,
    mot_de_passe_temporaire BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Table des actualités
CREATE TABLE IF NOT EXISTS actualites (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    category ENUM('formation', 'infrastructure', 'partenariat', 'pedagogie', 'vie-etudiante') DEFAULT 'formation',
    status ENUM('brouillon', 'publié') DEFAULT 'brouillon',
    image_url VARCHAR(500),
    author VARCHAR(100),
    date_publication DATE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER,
    FOREIGN KEY (created_by) REFERENCES utilisateurs(id)
);

-- Table des formations
CREATE TABLE IF NOT EXISTS formations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    level ENUM('technicien', ' qualification', 'technicien specialise', 'SPECIALISATION ') NOT NULL,
    duration VARCHAR(50),
    prerequisites TEXT,
    objectives TEXT,
    program TEXT,
    career TEXT,
    status ENUM('brouillon', 'publié') DEFAULT 'brouillon',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER,
    FOREIGN KEY (created_by) REFERENCES utilisateurs(id)
);

-- Table des événements
CREATE TABLE IF NOT EXISTS evenements (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    date_evenement DATE NOT NULL,
    lieu VARCHAR(255),
    type ENUM('Salon', 'Conférence', 'Portes ouvertes', 'Sortie pédagogique', 'Concours', 'Cérémonie') DEFAULT 'Conférence',
    participants TEXT,
    contact VARCHAR(255),
    status ENUM('brouillon', 'publié') DEFAULT 'brouillon',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER,
    FOREIGN KEY (created_by) REFERENCES utilisateurs(id)
);

-- Table des emplois du temps
CREATE TABLE IF NOT EXISTS emplois_du_temps (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    filiere VARCHAR(100) NOT NULL,
    groupe VARCHAR(100) NOT NULL,
    semaine VARCHAR(100) NOT NULL,
    file_name VARCHAR(255),
    file_path VARCHAR(500),
    file_size INTEGER,
    description TEXT,
    status ENUM('brouillon', 'publié') DEFAULT 'brouillon',
    upload_date DATE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER,
    FOREIGN KEY (created_by) REFERENCES utilisateurs(id)
);

-- Table des sessions de connexion (pour la sécurité)
CREATE TABLE IF NOT EXISTS sessions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    expires_at DATETIME NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES utilisateurs(id) ON DELETE CASCADE
);

-- Index pour améliorer les performances
CREATE INDEX IF NOT EXISTS idx_actualites_status ON actualites(status);
CREATE INDEX IF NOT EXISTS idx_actualites_category ON actualites(category);
CREATE INDEX IF NOT EXISTS idx_actualites_date ON actualites(date_publication);
CREATE INDEX IF NOT EXISTS idx_formations_level ON formations(level);
CREATE INDEX IF NOT EXISTS idx_formations_status ON formations(status);
CREATE INDEX IF NOT EXISTS idx_evenements_date ON evenements(date_evenement);
CREATE INDEX IF NOT EXISTS idx_evenements_status ON evenements(status);
CREATE INDEX IF NOT EXISTS idx_emplois_filiere ON emplois_du_temps(filiere);
CREATE INDEX IF NOT EXISTS idx_emplois_groupe ON emplois_du_temps(groupe);
CREATE INDEX IF NOT EXISTS idx_emplois_status ON emplois_du_temps(status);
CREATE INDEX IF NOT EXISTS idx_utilisateurs_email ON utilisateurs(email);
CREATE INDEX IF NOT EXISTS idx_utilisateurs_status ON utilisateurs(status);
CREATE INDEX IF NOT EXISTS idx_sessions_token ON sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_sessions_expires ON sessions(expires_at);
