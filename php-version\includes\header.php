<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - ' : ''; ?>Centre de Formation Agricole CMC</title>
    
    <!-- CSS principal -->
    <link rel="stylesheet" href="/php-version/assets/css/style.css">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icons (Lucide) -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    
    <!-- Meta tags -->
    <meta name="description" content="Centre de Formation Agricole CMC - Formations en agriculture, élevage et agroalimentaire">
    <meta name="keywords" content="formation agricole, BTS ACSE, CAP agriculture, Bac Pro CGEA">
    <meta name="author" content="Centre de Formation Agricole CMC">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/php-version/assets/images/favicon.ico">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <!-- Logo -->
            <div class="nav-logo">
                <a href="/php-version/index.php">
                    <img src="/php-version/assets/images/logo.png" alt="CMC Logo" class="logo-img">
                    <span class="logo-text">CMC</span>
                </a>
            </div>
            
            <!-- Menu principal -->
            <div class="nav-menu" id="nav-menu">
                <ul class="nav-list">
                    <li class="nav-item">
                        <a href="/php-version/index.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'index.php' ? 'active' : ''; ?>">
                            Accueil
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="/php-version/pages/formations.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'formations.php' ? 'active' : ''; ?>">
                            Formations
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="/php-version/pages/actualites.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'actualites.php' ? 'active' : ''; ?>">
                            Actualités
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="/php-version/pages/evenements.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'evenements.php' ? 'active' : ''; ?>">
                            Événements
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="/php-version/pages/emplois-du-temps.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'emplois-du-temps.php' ? 'active' : ''; ?>">
                            Emplois du temps
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="/php-version/pages/contact.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'contact.php' ? 'active' : ''; ?>">
                            Contact
                        </a>
                    </li>
                </ul>
                
                <!-- Bouton Admin -->
                <div class="nav-admin">
                    <a href="/php-version/admin/login.php" class="btn-admin">
                        <i data-lucide="settings"></i>
                        Administration
                    </a>
                </div>
            </div>
            
            <!-- Menu mobile toggle -->
            <div class="nav-toggle" id="nav-toggle">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- Main content wrapper -->
    <main class="main-content"><?php // Le contenu de la page sera inséré ici ?>
