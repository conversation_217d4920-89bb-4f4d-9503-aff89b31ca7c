<?php
require_once 'config/database.php';

$page_title = 'Accueil';

// Récupérer les dernières actualités
$db = getDB();
$actualites = $db->select(
    "SELECT * FROM actualites WHERE status = 'publié' ORDER BY date_publication DESC LIMIT 3"
);

// Récupérer les formations
$formations = $db->select(
    "SELECT * FROM formations WHERE status = 'publié' ORDER BY level, title LIMIT 3"
);

// Récupérer les prochains événements
$evenements = $db->select(
    "SELECT * FROM evenements WHERE status = 'publié' AND date_evenement >= CURDATE() ORDER BY date_evenement ASC LIMIT 3"
);

include 'includes/header.php';
?>

<!-- Hero Section -->
<section class="hero">
    <div class="hero-background">
        <div class="hero-overlay"></div>
        <img src="/php-version/assets/images/hero-agriculture.jpg" alt="Agriculture" class="hero-image">
    </div>
    
    <div class="hero-content">
        <div class="container">
            <div class="hero-text">
                <h1 class="hero-title">
                    Centre de Formation Agricole
                    <span class="hero-highlight">CMC</span>
                </h1>
                <p class="hero-description">
                    Formez-vous aux métiers de l'agriculture moderne avec nos programmes 
                    d'excellence : CAP, Bac Pro, BTS. Rejoignez une formation qui allie 
                    théorie et pratique pour votre réussite professionnelle.
                </p>
                <div class="hero-actions">
                    <a href="/php-version/pages/formations.php" class="btn btn-primary btn-lg">
                        <i data-lucide="graduation-cap"></i>
                        Découvrir nos formations
                    </a>
                    <a href="/php-version/pages/contact.php" class="btn btn-outline btn-lg">
                        <i data-lucide="phone"></i>
                        Nous contacter
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Section Statistiques -->
<section class="stats-section">
    <div class="container">
        <div class="stats-grid">
            <div class="stat-item">
                <div class="stat-icon">
                    <i data-lucide="users"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number">500+</div>
                    <div class="stat-label">Étudiants formés</div>
                </div>
            </div>
            
            <div class="stat-item">
                <div class="stat-icon">
                    <i data-lucide="award"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number">95%</div>
                    <div class="stat-label">Taux de réussite</div>
                </div>
            </div>
            
            <div class="stat-item">
                <div class="stat-icon">
                    <i data-lucide="briefcase"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number">85%</div>
                    <div class="stat-label">Insertion professionnelle</div>
                </div>
            </div>
            
            <div class="stat-item">
                <div class="stat-icon">
                    <i data-lucide="calendar"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number">25</div>
                    <div class="stat-label">Années d'expérience</div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Section Formations -->
<section class="section formations-section">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title">Nos Formations</h2>
            <p class="section-description">
                Découvrez nos programmes de formation adaptés à tous les niveaux, 
                du CAP au BTS, pour vous préparer aux métiers de l'agriculture.
            </p>
        </div>
        
        <div class="formations-grid">
            <?php foreach ($formations as $formation): ?>
            <div class="formation-card">
                <div class="formation-header">
                    <div class="formation-level">
                        <span class="badge badge-info"><?php echo htmlspecialchars($formation['level']); ?></span>
                    </div>
                    <h3 class="formation-title"><?php echo htmlspecialchars($formation['title']); ?></h3>
                </div>
                
                <div class="formation-content">
                    <p class="formation-description">
                        <?php echo htmlspecialchars(substr($formation['description'], 0, 150)) . '...'; ?>
                    </p>
                    
                    <div class="formation-details">
                        <div class="formation-detail">
                            <i data-lucide="clock"></i>
                            <span><?php echo htmlspecialchars($formation['duration']); ?></span>
                        </div>
                    </div>
                </div>
                
                <div class="formation-footer">
                    <a href="/php-version/pages/formation-detail.php?id=<?php echo $formation['id']; ?>" class="btn btn-primary">
                        En savoir plus
                        <i data-lucide="arrow-right"></i>
                    </a>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        
        <div class="section-footer">
            <a href="/php-version/pages/formations.php" class="btn btn-outline">
                Voir toutes les formations
                <i data-lucide="arrow-right"></i>
            </a>
        </div>
    </div>
</section>

<!-- Section Actualités -->
<section class="section actualites-section">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title">Actualités</h2>
            <p class="section-description">
                Restez informé de la vie du centre, des nouveautés et des événements à venir.
            </p>
        </div>
        
        <div class="actualites-grid">
            <?php foreach ($actualites as $actualite): ?>
            <article class="actualite-card">
                <?php if ($actualite['image_url']): ?>
                <div class="actualite-image">
                    <img src="<?php echo htmlspecialchars($actualite['image_url']); ?>" 
                         alt="<?php echo htmlspecialchars($actualite['title']); ?>">
                </div>
                <?php endif; ?>
                
                <div class="actualite-content">
                    <div class="actualite-meta">
                        <span class="badge badge-<?php echo $actualite['category'] == 'formation' ? 'info' : 'success'; ?>">
                            <?php echo ucfirst(htmlspecialchars($actualite['category'])); ?>
                        </span>
                        <time class="actualite-date">
                            <?php echo date('d/m/Y', strtotime($actualite['date_publication'])); ?>
                        </time>
                    </div>
                    
                    <h3 class="actualite-title">
                        <a href="/php-version/pages/actualite-detail.php?id=<?php echo $actualite['id']; ?>">
                            <?php echo htmlspecialchars($actualite['title']); ?>
                        </a>
                    </h3>
                    
                    <p class="actualite-excerpt">
                        <?php echo htmlspecialchars(substr($actualite['content'], 0, 120)) . '...'; ?>
                    </p>
                    
                    <div class="actualite-footer">
                        <span class="actualite-author">
                            <i data-lucide="user"></i>
                            <?php echo htmlspecialchars($actualite['author']); ?>
                        </span>
                        <a href="/php-version/pages/actualite-detail.php?id=<?php echo $actualite['id']; ?>" class="actualite-link">
                            Lire la suite
                            <i data-lucide="arrow-right"></i>
                        </a>
                    </div>
                </div>
            </article>
            <?php endforeach; ?>
        </div>
        
        <div class="section-footer">
            <a href="/php-version/pages/actualites.php" class="btn btn-outline">
                Toutes les actualités
                <i data-lucide="arrow-right"></i>
            </a>
        </div>
    </div>
</section>

<!-- Section Événements -->
<?php if (!empty($evenements)): ?>
<section class="section evenements-section">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title">Prochains Événements</h2>
            <p class="section-description">
                Ne manquez pas nos événements : salons, portes ouvertes, conférences...
            </p>
        </div>
        
        <div class="evenements-grid">
            <?php foreach ($evenements as $evenement): ?>
            <div class="evenement-card">
                <div class="evenement-date">
                    <div class="date-day"><?php echo date('d', strtotime($evenement['date_evenement'])); ?></div>
                    <div class="date-month"><?php echo date('M', strtotime($evenement['date_evenement'])); ?></div>
                </div>
                
                <div class="evenement-content">
                    <div class="evenement-meta">
                        <span class="badge badge-warning"><?php echo htmlspecialchars($evenement['type']); ?></span>
                    </div>
                    
                    <h3 class="evenement-title"><?php echo htmlspecialchars($evenement['title']); ?></h3>
                    
                    <div class="evenement-details">
                        <div class="evenement-detail">
                            <i data-lucide="map-pin"></i>
                            <span><?php echo htmlspecialchars($evenement['lieu']); ?></span>
                        </div>
                        <div class="evenement-detail">
                            <i data-lucide="users"></i>
                            <span><?php echo htmlspecialchars($evenement['participants']); ?></span>
                        </div>
                    </div>
                    
                    <p class="evenement-description">
                        <?php echo htmlspecialchars(substr($evenement['description'], 0, 100)) . '...'; ?>
                    </p>
                </div>
                
                <div class="evenement-footer">
                    <a href="/php-version/pages/evenement-detail.php?id=<?php echo $evenement['id']; ?>" class="btn btn-primary btn-sm">
                        Plus d'infos
                    </a>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        
        <div class="section-footer">
            <a href="/php-version/pages/evenements.php" class="btn btn-outline">
                Tous les événements
                <i data-lucide="arrow-right"></i>
            </a>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Section CTA -->
<section class="cta-section">
    <div class="container">
        <div class="cta-content">
            <h2 class="cta-title">Prêt à commencer votre formation ?</h2>
            <p class="cta-description">
                Rejoignez-nous et donnez un nouvel élan à votre carrière dans l'agriculture. 
                Nos conseillers sont là pour vous accompagner dans votre projet.
            </p>
            <div class="cta-actions">
                <a href="/php-version/pages/contact.php" class="btn btn-primary btn-lg">
                    <i data-lucide="phone"></i>
                    Contactez-nous
                </a>
                <a href="/php-version/pages/emplois-du-temps.php" class="btn btn-outline btn-lg">
                    <i data-lucide="calendar"></i>
                    Voir les emplois du temps
                </a>
            </div>
        </div>
    </div>
</section>

<?php include 'includes/footer.php'; ?>
