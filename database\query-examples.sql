-- Exemples de requêtes SQL pour la base de données CMC
-- Utilisez ces requêtes avec: sqlite3 database/cmc_database.sqlite

-- ========================================
-- REQUÊTES DE CONSULTATION
-- ========================================

-- Afficher tous les utilisateurs
SELECT 'UTILISATEURS:' as section;
SELECT id, nom, prenom, email, role, status FROM utilisateurs;

-- Afficher toutes les actualités publiées
SELECT 'ACTUALITÉS PUBLIÉES:' as section;
SELECT id, title, category, author, date_publication 
FROM actualites 
WHERE status = 'publié' 
ORDER BY date_publication DESC;

-- Afficher toutes les formations
SELECT 'FORMATIONS:' as section;
SELECT id, title, level, duration, status 
FROM formations 
ORDER BY level, title;

-- Afficher les événements à venir
SELECT 'ÉVÉNEMENTS:' as section;
SELECT id, title, date_evenement, lieu, type, status 
FROM evenements 
ORDER BY date_evenement;

-- Afficher les emplois du temps par filière
SELECT 'EMPLOIS DU TEMPS:' as section;
SELECT id, filiere, groupe, semaine, file_name, status 
FROM emplois_du_temps 
ORDER BY filiere, groupe;

-- ========================================
-- REQUÊTES STATISTIQUES
-- ========================================

-- Nombre d'éléments par table
SELECT 'STATISTIQUES:' as section;
SELECT 'Utilisateurs' as table_name, COUNT(*) as count FROM utilisateurs
UNION ALL
SELECT 'Actualités', COUNT(*) FROM actualites
UNION ALL
SELECT 'Formations', COUNT(*) FROM formations
UNION ALL
SELECT 'Événements', COUNT(*) FROM evenements
UNION ALL
SELECT 'Emplois du temps', COUNT(*) FROM emplois_du_temps;

-- Actualités par catégorie
SELECT 'ACTUALITÉS PAR CATÉGORIE:' as section;
SELECT category, COUNT(*) as count 
FROM actualites 
GROUP BY category 
ORDER BY count DESC;

-- Formations par niveau
SELECT 'FORMATIONS PAR NIVEAU:' as section;
SELECT level, COUNT(*) as count 
FROM formations 
GROUP BY level 
ORDER BY count DESC;

-- Emplois du temps par filière
SELECT 'EMPLOIS DU TEMPS PAR FILIÈRE:' as section;
SELECT filiere, COUNT(*) as count 
FROM emplois_du_temps 
GROUP BY filiere 
ORDER BY count DESC;

-- ========================================
-- REQUÊTES DE RECHERCHE
-- ========================================

-- Rechercher dans les actualités (exemple)
-- SELECT * FROM actualites WHERE title LIKE '%agriculture%' OR content LIKE '%agriculture%';

-- Rechercher des formations par niveau (exemple)
-- SELECT * FROM formations WHERE level = 'BTS';

-- Rechercher des emplois du temps par filière (exemple)
-- SELECT * FROM emplois_du_temps WHERE filiere = 'BTS ACSE';

-- ========================================
-- REQUÊTES DE MAINTENANCE
-- ========================================

-- Vérifier l'intégrité des données
SELECT 'VÉRIFICATION INTÉGRITÉ:' as section;

-- Actualités sans créateur
SELECT 'Actualités sans créateur:' as check_name, COUNT(*) as count
FROM actualites a 
LEFT JOIN utilisateurs u ON a.created_by = u.id 
WHERE u.id IS NULL;

-- Formations sans créateur
SELECT 'Formations sans créateur:' as check_name, COUNT(*) as count
FROM formations f 
LEFT JOIN utilisateurs u ON f.created_by = u.id 
WHERE u.id IS NULL;

-- Événements sans créateur
SELECT 'Événements sans créateur:' as check_name, COUNT(*) as count
FROM evenements e 
LEFT JOIN utilisateurs u ON e.created_by = u.id 
WHERE u.id IS NULL;

-- Emplois du temps sans créateur
SELECT 'Emplois du temps sans créateur:' as check_name, COUNT(*) as count
FROM emplois_du_temps et 
LEFT JOIN utilisateurs u ON et.created_by = u.id 
WHERE u.id IS NULL;
